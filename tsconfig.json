{"extends": "@tsconfig/node20/tsconfig.json", "compilerOptions": {"moduleResolution": "node", "target": "ESNext", "module": "CommonJS", "lib": ["ESNext", "DOM", "ESNext.AsyncIterable"], "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "sourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "types": ["node", "jest"], "outDir": "dist", "baseUrl": "src"}, "include": ["src/**/*.ts", "src/**/*.js", "infra/**/*.ts"], "exclude": ["cdk.out"]}