import { Document, Types } from 'mongoose';

export interface CoverageInterface extends Document<Types.ObjectId> {
  warrantyType?: string | null;
  warrantyExpDate?: number | null;
  extendedWarrantyExpDate?: number | null;
  contractId?: string | null;
  contractType?: string | null;
  contractEndDate?: number | null;
  assetId: string;
  serialNumber: string;
  startDate: number;
  endDate?: number | null;
  contractStatus?: string | null;
  contractCanceledDate?: number | null;
}
