import { Document, Types } from 'mongoose';

export interface Translations {
  ja_JP?: {
    possibleCause?: string;
    text?: string;
  };
}

export interface AlarmMetadataInterface extends Document<Types.ObjectId> {
  alarmId: string;
  alarmCode: string;
  category: string;
  chillerModel: string;
  controllerType: string;
  level: number;
  possibleCause: string;
  resetType: string;
  text: string;
  type: string;
  translations?: Translations;
}
