import type { FilterQuery } from 'mongoose';
import { FilterCriteria } from 'interfaces/pagination.interface';

const EXCLUSIVE_FILTER_FIELDS = ['series'];

export const createAuthQuery = <T>(filter: FilterCriteria[]): FilterQuery<T>[] => {
  const [exclusiveFilters, nonExclusiveFilters] = filter.reduce(
    (res, f) => {
      res[EXCLUSIVE_FILTER_FIELDS.includes(f.field) ? 0 : 1].push(f);
      return res;
    },
    [[], []] as [FilterCriteria[], FilterCriteria[]],
  );

  const match: FilterQuery<T>[] = [];
  if (nonExclusiveFilters.length > 0) {
    const nonExclusive = nonExclusiveFilters.reduce((res, { field, options }) => {
      return {
        ...res,
        [field]: {
          $in: options.map((option) => {
            return option !== 'null' ? option : null;
          }),
        },
      };
    }, {} as FilterQuery<T>);
    match.push(nonExclusive);
  }
  const seriesFilter = exclusiveFilters.find((f) => f.field === 'series');
  if (seriesFilter) {
    match.push({
      $or: [
        {
          series: { $eq: null },
          modelNumber: { $in: seriesFilter.options.map((option) => new RegExp(`^(${option})`)) },
        },
        { series: { $in: seriesFilter.options.map((option) => new RegExp(`^(${option})`)) } },
      ],
    });
  }

  return match;
};
