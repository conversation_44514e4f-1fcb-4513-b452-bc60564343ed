import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { AlarmRepository } from '../../repositories/alarm.repository';
import { AlarmModel } from '../../schemas/alarm.schema';
import { AcknowledgementModel } from '../../schemas/acknowledgement.schema';
import { AcknowledgementMockData } from '../mock/Acknowledgement.mock';
import { AlarmMockData } from '../mock/Alarm.mock';

describe('AlarmRepository', () => {
  let mongoServer: MongoMemoryServer;
  let repository: AlarmRepository;
  const assetsRequest: string[] = ['eceba0fd-5b80-4a1a-8a4b-9874f82e06b6'];
  jest.clearAllMocks();

  beforeAll(async () => {
    const actualMongoose = jest.requireActual('mongoose');
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await actualMongoose.connect(uri);
  });

  beforeEach(async () => {
    const alarmData = await AlarmModel.insertMany(AlarmMockData);
    const ackData = await AcknowledgementModel.insertMany(AcknowledgementMockData);
    await AlarmModel.updateOne({ _id: alarmData[0]._id }, { acknowledgementId: ackData[0]._id });

    repository = new AlarmRepository(AlarmModel);
  });

  afterEach(async () => {
    await AlarmModel.deleteMany({});
    await AcknowledgementModel.deleteMany({});
  });

  it('should return acknowledgement data if fetchAcknowledgementDataIfExists true and acknowledgement data exists', async () => {
    const alarmData: any = await repository.findManyByAssetIds({
      assetIds: assetsRequest,
      from: 1709228300000,
      isGroupedByFullCode: false,
      fetchAcknowledgementDataIfExists: true,
    });
    expect(alarmData[0].acknowledgement).toBeDefined();
  });

  it('should not return acknowledgement data if fetchAcknowledgementDataIfExists false and acknowledgement data exists', async () => {
    const alarmData: any = await repository.findManyByAssetIds({
      assetIds: assetsRequest,
      from: 1709228300000,
      isGroupedByFullCode: false,
      fetchAcknowledgementDataIfExists: false,
    });
    expect(alarmData[0].acknowledgement).toBeUndefined();
  });

  it('should return alarms data for getAlarmsByAssetIds api', async () => {
    const alarmData: any = await repository.getAlarmsByAssetIds({
      assetIds: assetsRequest,
      startDate: 1709228300000,
      endDate: 1709229300000,
    });
    expect(alarmData?.length).toEqual(1);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });
});
