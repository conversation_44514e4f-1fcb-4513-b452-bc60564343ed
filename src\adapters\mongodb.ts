import { connect, connection, ConnectOptions, Mongoose, set } from 'mongoose';

import { DatabaseError } from '../errors';
import { SecretManager } from '../services';
import { STAGE } from '../enums';

export interface IDbAdapter {
  connect(): Promise<Mongoose | undefined>;
  closeConnection(): Promise<void>;
}

const secretManager = new SecretManager();

export class MongoDBAdapter implements IDbAdapter {
  public async connect(): Promise<Mongoose | undefined> {
    let connection;

    try {
      let credentials;
      if (process.env.STAGE === STAGE.LOCAL) {
        credentials = {
          username: process.env.DB_USER,
          password: process.env.DB_PASSWORD,
        };
      } else {
        const secretResponse = await secretManager.getSecretValue(process.env.SECRET_DB_KEY || '');
        credentials = JSON.parse(secretResponse.SecretString || '');
      }

      const dbUrl = await MongoDBAdapter.getConnectionURL(credentials?.username, credentials?.password);

      set('strictQuery', false);
      set('debug', (collectionName, method, query, doc) => {
        // eslint-disable-next-line
        console.log(`${collectionName}.${method}`, JSON.stringify(query), doc);
      });
      connection = await connect(dbUrl, {
        dbName: process.env.DB_NAME,
        maxPoolSize: 10,
        authMechanism: 'SCRAM-SHA-1',
        authSource: 'admin'
      } as ConnectOptions);
    } catch (error) {
      throw new DatabaseError(
        error instanceof Error ? `DB connection error: ${error.message}` : 'Untraceable connection error',
      );
    }

    return connection;
  }

  private static async getConnectionURL(username: string, password: string): Promise<string> {
    return (process.env.DB_URL ?? '').replace('<username>', username).replace('<password>', password);
  }

  async closeConnection(): Promise<void> {
    await connection?.close();
  }
}
