/* eslint-disable sonarjs/no-duplicate-string */
import { ClientSession, FilterQuery, Model, PipelineStage, Types } from 'mongoose';
import { AlarmBasicInterface, AlarmInterface } from '../interfaces';
import { FilterCriteria, OrderCriteria, PageCriteria, SortDirection } from '../interfaces/pagination.interface';
import { parseFilterOption } from '../helpers';
import { tableName as coverageTableName } from '../schemas';
import { LANGUAGE_LOCALE } from '../enums/languageLocale.enum';
import { createAuthQuery } from '../helpers/createAuthQuery';
import { AlarmType } from '../enums/alarmType.enum';

type AlarmGroupedBySite = {
  _id: string;
  assetId: string;
  siteId: string;
  series: string;
  controller: string;
  type: string;
};

type GetAlarmAlertCountBySiteListResponse = {
  _id: string;
  activeAlarms: AlarmGroupedBySite[];
  activeAlerts: AlarmGroupedBySite[];
  activeAlarmsCountLast24Hours: AlarmGroupedBySite[];
  activeGwAlarms?: AlarmGroupedBySite[];
  activeGwAlerts?: AlarmGroupedBySite[];
  activeGwAlarmsCountLast24Hours?: AlarmGroupedBySite[];
  activeUcAlarms: AlarmGroupedBySite[];
  activeUcAlerts: AlarmGroupedBySite[];
};

export interface IFindManyCriteria {
  assetIds: string[];
  from: number;
  to?: number;
  searchString?: string;
  page?: PageCriteria;
  filter?: FilterCriteria[];
  order?: OrderCriteria[];
  isGroupedByFullCode: boolean;
  fetchAcknowledgementDataIfExists?: boolean;
}

const ALARM_CRITICALITY = {
  critical: 1,
  highRisk: 2,
  warning: 3,
  nonOptimal: 4,
};

const ALARM_TYPE = {
  alarm: 'Alarm',
  alert: 'Alert',
};

const CONTROLLER_TYPE = {
  uc: 'UC',
  gw: 'GW',
};
const CjcGwEquipmentFamily = 'CJC_EYE';
const ACTIVE_STATUS = 'Active';
const HIGH_RISK_THRESHOLD = 3;
const CRITICAL_THRESHOLD = 0;
const GRACE_PERIOD = 7776000000; // 90 days in milliseconds
const complexFilterNames = ['coverage'];
const CUSTOM_ALARM_KEY = 'CUSTOM_ALARM';

export class AlarmRepository {
  // eslint-disable-next-line
  constructor(private readonly model: Model<AlarmInterface>) { }

  async findAll(): Promise<AlarmInterface[]> {
    return this.model.find().exec();
  }

  async findById(_id: string): Promise<AlarmInterface | null> {
    return this.model.findById(_id).exec();
  }

  async findByIds(ids: string[]): Promise<AlarmInterface[]> {
    return this.model
      .find({
        _id: {
          $in: ids.map((i) => new Types.ObjectId(i)),
        },
      })
      .exec();
  }

  async findAlarmByAssetIdAndCodeAndStatus(assetId: string, code: string, status: string): Promise<AlarmInterface[]> {
    return this.model
      .find({
        assetId,
        code,
        status,
      })
      .exec();
  }

  async create(payload: any, session?: ClientSession): Promise<AlarmInterface | null> {
    const [response] = await this.model.create([payload], { session });

    return response;
  }

  async update(_id: string, dto: any, session?: ClientSession): Promise<AlarmInterface | null> {
    await this.model
      .updateOne(
        {
          _id: new Types.ObjectId(_id),
        },
        dto,
        { session, new: true },
      )
      .exec();

    return this.findById(_id);
  }

  async updateMany(_ids: string[], acknowledgementId: string, session?: ClientSession): Promise<AlarmInterface[]> {
    const objectIds = _ids.map((_id) => {
      return new Types.ObjectId(_id);
    });
    await this.model
      .updateMany(
        {
          _id: { $in: objectIds },
        },
        { acknowledgementId },
        { session, new: true },
      )
      .exec();

    return this.model.find({ _id: { $in: _ids } }).exec();
  }

  async updateBatch(
    _ids: string[],
    payload: Partial<AlarmInterface>,
    session?: ClientSession,
  ): Promise<AlarmInterface[]> {
    await this.model
      .updateMany(
        {
          _id: { $in: _ids },
        },
        payload,
        { session, new: true },
      )
      .exec();

    return this.model.find({ _id: { $in: _ids } }).exec();
  }

  async delete(_id: string, session?: ClientSession): Promise<AlarmInterface | null> {
    return this.model.findByIdAndDelete(_id, { session }).exec();
  }

  async createOrUpdate(_id: string, payload: any) {
    await this.model.updateOne({ _id: new Types.ObjectId(_id) }, payload, {
      upsert: true,
      new: true,
    });

    return this.findById(_id);
  }

  async findByTimestamp(from: number): Promise<AlarmInterface[]> {
    const dayBefore = new Date(from);
    dayBefore.setDate(dayBefore.getDate() - 1);

    return this.model
      .find({
        timestamp: {
          $gte: +dayBefore,
        },
      })
      .exec();
  }

  async getTotalCountByCriteria(criteria: IFindManyCriteria, alarmType?: AlarmType): Promise<number> {
    const aggregateRules = this.generateAggregateRules(criteria, undefined, alarmType);

    aggregateRules.push({
      $count: 'count',
    });

    const result = await this.model.aggregate(aggregateRules);

    return result.shift()?.count ?? 0;
  }

  async findManyByAssetIds(
    criteria: IFindManyCriteria,
    language?: string,
    alarmType?: AlarmType,
  ): Promise<AlarmInterface[]> {
    const aggregateRules = this.generateAggregateRules(criteria, language, alarmType);

    if (criteria.isGroupedByFullCode) {
      aggregateRules.push({
        $replaceRoot: {
          newRoot: {
            // eslint-disable-next-line sonarjs/no-duplicate-string
            $mergeObjects: ['$doc', { groupedCount: '$groupedCount', timestamp: '$timestamp' }],
          },
        },
      });
    }

    let sortSteps: PipelineStage[] = [
      {
        $sort: {
          timestamp: SortDirection.DESC,
        },
      },
    ];

    if (criteria.order && criteria.order.length) {
      sortSteps = this.createOrderQuerySteps(criteria.order);
    }

    aggregateRules.push(
      ...sortSteps,
      {
        $skip: criteria.page?.offset ?? 0,
      },
      {
        $limit: criteria.page?.limit ?? Number.MAX_SAFE_INTEGER,
      },
    );

    return this.model.aggregate(aggregateRules).exec();
  }

  async generateDataForATC(
    dateFrom: number,
    searchString?: string,
    page?: PageCriteria,
    filter?: FilterCriteria[],
    order?: OrderCriteria[],
    authScopeFilter?: FilterCriteria[],
  ): Promise<
    {
      _id: string;
      siteName: string;
      siteAddress: string;
      siteCountry: string;
      chillersInAlarm: number;
      totalCriticalAlarms: number;
      totalHightRiskAlarms: number;
      lastAlarmTime: number;
      chillers: { assetId: string; cioTags: string }[];
      chillerName: string | null;
      serialNumber: string | null;
      model: string | null;
      modelNumber: string | null;
      fullCode: string | null;
      totalChillers: number;
      coverage: Array<{
        warrantyType?: string;
        warrantExpDate?: number;
        extendedWarrantyExpDate?: number;
        contractId?: string;
        contractType?: string;
        contractEndDate?: number;
        contractStatus?: string;
        contractCanceledDate?: number;
        assetId: string;
        serialNumber: string;
        startDate: number;
        endDate?: number;
      }>;
    }[]
  > {
    let match: FilterQuery<AlarmInterface> = {
      timestamp: {
        $gte: dateFrom,
      },
      acknowledgementId: null,
      isMuted: { $ne: true },
      status: ACTIVE_STATUS,
    };

    if (searchString) {
      const strWithEscapedChars = `\\Q${searchString}\\E`;
      const regExpFilter = {
        $regex: strWithEscapedChars,
        $options: 'i',
      };

      match.$or = [{ siteName: regExpFilter }, { siteAddress: regExpFilter }, { siteCountry: regExpFilter }];
    }

    let coverageFilterOption: string | null = null;

    if (filter?.length) {
      const basicFilters = filter.filter(({ field }) => !complexFilterNames.includes(field));
      const filterList = this.createFilterQuery(basicFilters);
      match = {
        ...match,
        ...filterList,
      };

      coverageFilterOption = filter.find(({ field }) => field === 'coverage')?.options?.at(0) ?? null;
    }

    if (authScopeFilter?.length) {
      const authFilter = createAuthQuery<AlarmInterface>(authScopeFilter);

      match = {
        $and: [match, ...authFilter],
      };
    }

    let sort: any = {
      lastAlarmTime: SortDirection.DESC,
    };

    if (order && order.length) {
      sort = this.createATCOrderQuery(order);
    }
    const HIGH_RISK_ALARMS = '$hightRiskAlarms';
    const CRITICAL_ALARMS = '$criticalAlarms';

    const matchConditionsToExcludeMC = [{ controller: { $ne: 'MC' } }, { controller: { $exists: false } }];

    return this.model
      .aggregate([
        {
          $match: match,
        },
        {
          $lookup: {
            from: coverageTableName,
            localField: 'assetId',
            foreignField: 'assetId',
            as: 'coverage',
          },
        },
        { $unwind: { path: '$coverage', preserveNullAndEmptyArrays: true } },
        {
          $sort: {
            'coverage.startDate': -1,
          },
        },
        {
          $addFields: {
            graceWarrantyDate: {
              $add: [{ $ifNull: ['$coverage.warrantyExpDate', 0] }, GRACE_PERIOD],
            },
            graceExtendedWarrantyDate: {
              $add: [{ $ifNull: ['$coverage.extendedWarrantyExpDate', 0] }, GRACE_PERIOD],
            },
            graceContractEndDate: {
              $add: [{ $ifNull: ['$coverage.contractEndDate', 0] }, GRACE_PERIOD],
            },
            // Add a field to identify if this record has valid coverage
            hasCoverage: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$coverage.endDate', null] },
                    {
                      $or: [
                        { $gt: [{ $add: [{ $ifNull: ['$coverage.warrantyExpDate', 0] }, GRACE_PERIOD] }, Date.now()] },
                        {
                          $gt: [
                            { $add: [{ $ifNull: ['$coverage.extendedWarrantyExpDate', 0] }, GRACE_PERIOD] },
                            Date.now(),
                          ],
                        },
                        {
                          $and: [
                            {
                              $gt: [
                                { $add: [{ $ifNull: ['$coverage.contractEndDate', 0] }, GRACE_PERIOD] },
                                Date.now(),
                              ],
                            },
                            {
                              $or: [
                                { $eq: ['$coverage.contractCanceledDate', null] },
                                { $gt: ['$coverage.contractCanceledDate', Date.now()] },
                              ],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
                then: true,
                else: false,
              },
            },
          },
        },
        // Sort by coverage start date
        { $sort: { 'coverage.startDate': -1 } },
        {
          $group: {
            _id: {
              alarmId: '$_id',
              assetId: '$assetId',
            },
            data: { $first: '$$ROOT' },
            coverage: {
              $first: '$coverage',
            },
            hasCoverage: { $first: '$hasCoverage' },
          },
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ['$data', { coverage: '$coverage', hasCoverage: '$hasCoverage' }] },
          },
        },
        {
          $match: {
            $or: [...matchConditionsToExcludeMC],
          },
        },
        {
          $group: {
            _id: {
              siteId: '$siteId',
              assetId: '$assetId',
              code: '$code',
              conditionId: '$conditionId',
            },
            cioTags: {
              $first: '$cioTags',
            },
            siteName: {
              $first: '$siteName',
            },
            siteAddress: {
              $first: '$siteAddress',
            },
            siteCountry: {
              $first: '$siteCountry',
            },
            criticalAlarms: {
              $sum: {
                $cond: [
                  {
                    $eq: ['$$ROOT.criticality', ALARM_CRITICALITY.critical],
                  },
                  1,
                  0,
                ],
              },
            },
            hightRiskAlarms: {
              $sum: {
                $cond: [
                  {
                    $eq: ['$$ROOT.criticality', ALARM_CRITICALITY.highRisk],
                  },
                  1,
                  0,
                ],
              },
            },
            lastAlarmTime: {
              $max: '$$ROOT.timestamp',
            },
            totalChillers: {
              $last: '$totalChillers',
            },
            coverage: {
              $first: '$coverage',
            },
            hasCoverage: {
              $first: '$hasCoverage',
            },
          },
        },
        {
          $group: {
            _id: {
              siteId: '$_id.siteId',
              assetId: '$_id.assetId',
            },
            cioTags: {
              $first: '$cioTags',
            },
            siteName: {
              $first: '$siteName',
            },
            siteAddress: {
              $first: '$siteAddress',
            },
            siteCountry: {
              $first: '$siteCountry',
            },
            criticalAlarms: {
              $max: '$criticalAlarms',
            },
            hightRiskAlarms: {
              $max: '$hightRiskAlarms',
            },
            totalCriticalAlarms: {
              $sum: CRITICAL_ALARMS,
            },
            totalHighRiskAlarms: {
              $sum: HIGH_RISK_ALARMS,
            },
            lastAlarmTime: {
              $max: '$lastAlarmTime',
            },
            totalChillers: {
              $last: '$totalChillers',
            },
            coverage: {
              $first: '$coverage',
            },
            hasCoverage: {
              $first: '$hasCoverage',
            },
          },
        },
        {
          $group: {
            _id: '$_id.siteId',
            siteName: {
              $first: '$siteName',
            },
            siteAddress: {
              $first: '$siteAddress',
            },
            siteCountry: {
              $first: '$siteCountry',
            },
            chillersInAlarm: {
              $sum: {
                $cond: [
                  {
                    $or: [{ $gt: [CRITICAL_ALARMS, 0] }, { $gt: [HIGH_RISK_ALARMS, 0] }],
                  },
                  1,
                  0,
                ],
              },
            },
            maxCriticalAlarms: {
              $max: CRITICAL_ALARMS,
            },
            maxHighRiskAlarms: {
              $max: HIGH_RISK_ALARMS,
            },
            totalCriticalAlarms: {
              $sum: '$totalCriticalAlarms',
            },
            totalHightRiskAlarms: {
              $sum: '$totalHighRiskAlarms',
            },
            lastAlarmTime: {
              $max: '$lastAlarmTime',
            },
            chillers: {
              $push: {
                assetId: '$_id.assetId',
                cioTags: '$cioTags',
              },
            },
            totalChillers: {
              $first: '$totalChillers',
            },
            coverage: {
              $push: '$coverage',
            },
            hasCoverage: {
              $push: '$hasCoverage',
            }
          },
        },
        {
          $project: {
            coverage: {
              $setDifference: ['$coverage', [null]],
            },
            data: '$$ROOT',
          },
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: ['$data', { coverage: '$coverage', hasCoverage: '$hasCoverage' }] },
          },
        },
        {
          $match: {
            $or: [
              { maxHighRiskAlarms: { $gt: HIGH_RISK_THRESHOLD } },
              { maxCriticalAlarms: { $gt: CRITICAL_THRESHOLD } },
            ],
            siteName: { $ne: null },
            // Apply coverage filter based on the coverageFilterOption
            ...(coverageFilterOption === 'true' ? { hasCoverage: true } : {}),
            ...(coverageFilterOption === 'false' ? { hasCoverage: false } : {}),
          },
        },
        {
          $sort: sort,
        },
        {
          $skip: page?.offset ?? 0,
        },
        {
          $limit: page?.limit ?? Number.MAX_SAFE_INTEGER,
        },
      ])
      .exec();
  }

  async getAlarmsCountBySiteId(
    siteId: string,
    dateFrom: number,
    authScopeFilter?: FilterCriteria[],
  ): Promise<
    {
      assetId: string;
      totalCriticalAlarms: number;
      totalHighRiskAlarms: number;
      totalUnacknowledgedAlarms: number;
    }[]
  > {
    const seriesAuthFilter = authScopeFilter?.find((filter) => filter.field === 'series');
    let authScopeMatch = {};
    if (seriesAuthFilter) {
      authScopeMatch = {
        $or: [
          {
            series: { $eq: null },
            modelNumber: { $in: seriesAuthFilter.options.map((option) => new RegExp(`^(${option})`)) },
          },
          { series: { $in: seriesAuthFilter.options.map((option) => new RegExp(`^(${option})`)) } },
        ],
      };
    }
    const aggregateRules = [
      {
        $match: {
          siteId,
          status: ACTIVE_STATUS,
          ...authScopeMatch,
        },
      },
      {
        $group: {
          _id: '$assetId',
          totalCriticalAlarms: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$resetAt', null] },
                    { $eq: ['$criticality', ALARM_CRITICALITY.critical] },
                    { $gte: ['$timestamp', dateFrom] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalHighRiskAlarms: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$resetAt', null] },
                    { $eq: ['$criticality', ALARM_CRITICALITY.highRisk] },
                    { $gte: ['$timestamp', dateFrom] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalUnacknowledgedAlarms: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gte: ['$timestamp', dateFrom] },
                    { $lte: ['$acknowledgementId', undefined] },
                    { $ne: ['$isMuted', true] },
                  ],
                },
                1,
                0,
              ],
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          assetId: '$_id',
          totalCriticalAlarms: {
            $ifNull: ['$totalCriticalAlarms', 0],
          },
          totalHighRiskAlarms: {
            $ifNull: ['$totalHighRiskAlarms', 0],
          },
          totalUnacknowledgedAlarms: {
            $ifNull: ['$totalUnacknowledgedAlarms', 0],
          },
        },
      },
    ];

    return this.model.aggregate(aggregateRules).exec();
  }

  /*
    Receive the number of raised alarms with additional split information about the criticality and types,
    and the most recent |numberMostRecentAlarms| alarms in the period
   */
  async getAlarmsSummaryByAssetId({
    assetIds,
    startDate,
    endDate,
    numberMostRecentAlarms,
    language,
  }: {
    assetIds: Array<string>;
    startDate: number;
    endDate: number;
    numberMostRecentAlarms: number;
    language?: string;
  }): Promise<
    Array<{
      assetId: string;
      totalAlarmType: number;
      totalAlertType: number;
      totalUnknownType: number;
      totalAlarms: number;
      totalCriticalAlarms: number;
      totalActiveCriticalAlarms: number;
      totalActiveCriticalAlerts: number;
      totalHighRiskAlarms: number;
      totalActiveHighRiskAlarms: number;
      totalActiveHighRiskAlerts: number;
      totalWarningAlarms: number;
      totalNonOptimalAlarms: number;
      totalOtherAlarms: number;
      mostRecentAlarms: Array<AlarmInterface>;
    }>
  > {
    const aggregateRules = [
      {
        $match: {
          assetId: {
            $in: assetIds,
          },
          status: ACTIVE_STATUS,
          timestamp: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      },
      { $sort: { timestamp: -1 as -1 } },
      {
        $group: {
          _id: '$assetId',
          totalAlarmType: {
            $sum: {
              $cond: [{ $eq: ['$type', ALARM_TYPE.alarm] }, 1, 0],
            },
          },
          totalAlertType: {
            $sum: {
              $cond: [{ $eq: ['$type', ALARM_TYPE.alert] }, 1, 0],
            },
          },
          totalUnknownType: {
            $sum: {
              $cond: [{ $eq: ['$type', null] }, 1, 0],
            },
          },
          totalAlarms: {
            $sum: 1,
          },
          totalCriticalAlarms: {
            $sum: {
              $cond: [{ $eq: ['$criticality', ALARM_CRITICALITY.critical] }, 1, 0],
            },
          },
          totalActiveCriticalAlarms: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$criticality', ALARM_CRITICALITY.critical] },
                    { $eq: ['$type', ALARM_TYPE.alarm] },
                    { $eq: ['$resetAt', null] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalActiveCriticalAlerts: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$criticality', ALARM_CRITICALITY.critical] },
                    { $eq: ['$type', ALARM_TYPE.alert] },
                    { $eq: ['$resetAt', null] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalHighRiskAlarms: {
            $sum: {
              $cond: [{ $eq: ['$criticality', ALARM_CRITICALITY.highRisk] }, 1, 0],
            },
          },
          totalActiveHighRiskAlarms: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$criticality', ALARM_CRITICALITY.highRisk] },
                    { $eq: ['$type', ALARM_TYPE.alarm] },
                    { $eq: ['$resetAt', null] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalActiveHighRiskAlerts: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$criticality', ALARM_CRITICALITY.highRisk] },
                    { $eq: ['$type', ALARM_TYPE.alert] },
                    { $eq: ['$resetAt', null] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalWarningAlarms: {
            $sum: {
              $cond: [{ $eq: ['$criticality', ALARM_CRITICALITY.warning] }, 1, 0],
            },
          },
          totalNonOptimalAlarms: {
            $sum: {
              $cond: [{ $eq: ['$criticality', ALARM_CRITICALITY.nonOptimal] }, 1, 0],
            },
          },
          totalOtherAlarms: {
            $sum: {
              $cond: [
                {
                  $not: {
                    $in: ['$criticality', Object.values(ALARM_CRITICALITY)],
                  },
                },
                1,
                0,
              ],
            },
          },
          alarms: {
            $push: '$$ROOT',
          },
        },
      },
      {
        $project: {
          _id: 0,
          assetId: '$_id',
          totalAlarmType: {
            $ifNull: ['$totalAlarmType', 0],
          },
          totalAlertType: {
            $ifNull: ['$totalAlertType', 0],
          },
          totalUnknownType: {
            $ifNull: ['$totalUnknownType', 0],
          },
          totalAlarms: {
            $ifNull: ['$totalAlarms', 0],
          },
          totalCriticalAlarms: {
            $ifNull: ['$totalCriticalAlarms', 0],
          },
          totalActiveCriticalAlarms: {
            $ifNull: ['$totalActiveCriticalAlarms', 0],
          },
          totalActiveCriticalAlerts: {
            $ifNull: ['$totalActiveCriticalAlerts', 0],
          },
          totalHighRiskAlarms: {
            $ifNull: ['$totalHighRiskAlarms', 0],
          },
          totalActiveHighRiskAlarms: {
            $ifNull: ['$totalActiveHighRiskAlarms', 0],
          },
          totalActiveHighRiskAlerts: {
            $ifNull: ['$totalActiveHighRiskAlerts', 0],
          },
          totalWarningAlarms: {
            $ifNull: ['$totalWarningAlarms', 0],
          },
          totalNonOptimalAlarms: {
            $ifNull: ['$totalNonOptimalAlarms', 0],
          },
          totalOtherAlarms: {
            $ifNull: ['$totalOtherAlarms', 0],
          },
          mostRecentAlarms: {
            $slice: ['$alarms', numberMostRecentAlarms],
          },
        },
      },
      {
        $addFields: {
          customTranslationLanguage: language ?? LANGUAGE_LOCALE.en_US,
        },
      },
      {
        $lookup: {
          from: 'Translations',
          localField: 'customTranslationLanguage',
          foreignField: 'language',
          as: 'customTranslationLanguageProperties',
        },
      },
      { $unwind: { path: '$customTranslationLanguageProperties', preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          mostRecentAlarms: {
            $map: {
              input: '$mostRecentAlarms',
              in: {
                $mergeObjects: [
                  '$$this',
                  {
                    $cond: [
                      { $eq: ['$$this.lookupKey', CUSTOM_ALARM_KEY] },
                      {
                        possibleCause: '$customTranslationLanguageProperties.possibleCauseTemplate',
                        description: '$customTranslationLanguageProperties.descriptionTextTemplate',
                        currentValue: `$$this.thresholdProperties.currentValue`,
                        conditionValue: `$$this.thresholdProperties.conditionValue`,
                        unitOfMeasure: { $ifNull: ['$$this.thresholdProperties.unitOfMeasure', ''] },
                      },
                      {},
                    ],
                  },
                ],
              },
            },
          },
        },
      },
    ];

    return this.model.aggregate(aggregateRules).exec();
  }

  private createATCOrderQuery(order: OrderCriteria[]): any {
    return order.reduce((sort: any, item: any) => {
      return {
        ...sort,
        [item.field]: item.direction,
      };
    }, {});
  }

  private createOrderQuerySteps(order: OrderCriteria[]) {
    let preSteps: PipelineStage[] = [];

    const sort = order.reduce<{
      [k: OrderCriteria['field']]: OrderCriteria['direction'];
    }>((sort, item) => {
      let { field } = item;
      if (field === 'resetAt') {
        preSteps = [
          {
            $addFields: {
              resetAtSortField: {
                $ifNull: ['$resetAt', Number.MAX_SAFE_INTEGER],
              },
            },
          },
        ];
        field = 'resetAtSortField';
      }

      return {
        ...sort,
        [field]: item.direction,
      };
    }, {});
    return [...preSteps, { $sort: sort }];
  }

  private createFilterQuery(filter: FilterCriteria[]): any {
    return filter.reduce((filter, item: FilterCriteria) => {
      return {
        ...filter,
        [item.field]: {
          $in: item.options.map((option) => parseFilterOption(item.field, option)),
        },
      };
    }, {});
  }

  private generateAggregateRules(
    options: IFindManyCriteria,
    language?: string,
    alarmType?: AlarmType,
  ): PipelineStage[] {
    let match: any = {
      assetId: { $in: options.assetIds },
      timestamp: {
        $gte: options.from,
        ...(options.to && { $lte: options.to }),
      },
      status: ACTIVE_STATUS,
    };

    if (alarmType) {
      match = {
        ...match,
        type: alarmType,
      };
    }

    if (options.searchString) {
      const regExp = new RegExp(options.searchString, 'i');

      match.$or = [
        { fullCode: regExp },
        { category: regExp },
        { description: regExp },
        { possibleCause: regExp },
        { serialNumber: regExp },
        { modelNumber: regExp },
      ];
    }

    if (options.filter && options.filter.length) {
      const filterList = this.createFilterQuery(options.filter);

      match = {
        ...match,
        ...filterList,
      };
    }

    let aggregateRules: any = [
      {
        $match: match,
      },
      {
        $lookup: {
          from: 'AlarmMetadata',
          localField: 'lookupKey',
          foreignField: 'lookupKey',
          as: 'metadata',
        },
      },
      { $unwind: { path: '$metadata', preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          customTranslationLanguage: language ?? LANGUAGE_LOCALE.en_US,
        },
      },
      {
        $lookup: {
          from: 'Translations',
          localField: 'customTranslationLanguage',
          foreignField: 'language',
          as: 'customTranslationLanguageProperties',
        },
      },
      { $unwind: { path: '$customTranslationLanguageProperties', preserveNullAndEmptyArrays: true } },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$$ROOT',
              {
                $cond: [
                  { $eq: ['$lookupKey', CUSTOM_ALARM_KEY] },
                  {
                    possibleCause: '$customTranslationLanguageProperties.possibleCauseTemplate',
                    description: '$customTranslationLanguageProperties.descriptionTextTemplate',
                    currentValue: `$thresholdProperties.currentValue`,
                    conditionValue: `$thresholdProperties.conditionValue`,
                    unitOfMeasure: { $ifNull: ['$thresholdProperties.unitOfMeasure', ''] },
                  },
                  {
                    criticality: '$metadata.level',
                    possibleCause: {
                      $cond: [
                        { $eq: [language, LANGUAGE_LOCALE.ja_JP] },
                        '$metadata.translations.ja_JP.possibleCause',
                        '$metadata.possibleCause',
                      ],
                    },
                    description: {
                      $cond: [
                        { $eq: [language, LANGUAGE_LOCALE.ja_JP] },
                        '$metadata.translations.ja_JP.text',
                        '$metadata.text',
                      ],
                    },
                    type: '$metadata.type',
                    category: '$metadata.category',
                    resetType: '$metadata.resetType',
                  },
                ],
              },
            ],
          },
        },
      },
    ];

    if (options.isGroupedByFullCode) {
      aggregateRules.push({
        $group: {
          _id: {
            fullCode: '$fullCode',
            conditionId: '$conditionId',
          },
          groupedCount: { $sum: 1 },
          timestamp: { $max: '$timestamp' },
          fullCode: { $first: '$fullCode' },
          doc: { $first: '$$ROOT' },
        },
      });
    }
    // Check if acknowledgement object need to be fetched
    if (options?.fetchAcknowledgementDataIfExists) {
      const fetchAcknowledgementQuery = [
        {
          $lookup: {
            from: 'Acknowledgement',
            localField: 'acknowledgementId',
            foreignField: '_id',
            as: 'acknowledgement',
          },
        },
        {
          $unwind: {
            path: '$acknowledgement',
            preserveNullAndEmptyArrays: true,
          },
        },
      ];
      aggregateRules = [...aggregateRules, ...fetchAcknowledgementQuery];
    }

    return aggregateRules;
  }

  async getAlarmAlertCountBySiteList(sites: string[]): Promise<GetAlarmAlertCountBySiteListResponse[]> {
    const aggregateRules: any = [
      {
        $match: {
          siteId: {
            $in: sites,
          },
          status: ACTIVE_STATUS,
          resetAt: null,
          chillerName: { $exists: true },
        },
      },
      {
        $project: {
          _id: 1,
          siteId: 1,
          type: 1,
          resetAt: 1,
          assetId: 1,
          chillerName: 1,
          controller: 1,
          series: 1,
          modelNumber: 1,
          edgeId: 1,
          edgeIsOnline: 1,
          equipmentFamily: 1,
        },
      },
      {
        $group: {
          _id: '$siteId',
          activeAlarms: {
            $push: {
              $cond: [{ $eq: ['$type', ALARM_TYPE.alarm] }, '$$ROOT', null],
            },
          },
          activeAlerts: {
            $push: {
              $cond: [{ $eq: ['$type', ALARM_TYPE.alert] }, '$$ROOT', null],
            },
          },
          activeAlarmsCountLast24Hours: {
            $push: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$type', ALARM_TYPE.alarm] },
                    { $gte: ['$timestamp', Date.now() - 24 * 60 * 60 * 1000] },
                  ],
                },
                '$$ROOT',
                null,
              ],
            },
          },
          activeGwAlarmsCountLast24Hours: {
            $push: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$type', ALARM_TYPE.alert] },
                    { $gte: ['$timestamp', Date.now() - 24 * 60 * 60 * 1000] },
                    {
                      $or: [
                        { $eq: ['$controller', CONTROLLER_TYPE.gw] },
                        { $eq: ['$equipmentFamily', CjcGwEquipmentFamily] },
                      ],
                    },
                  ],
                },
                '$$ROOT',
                null,
              ],
            },
          },
          activeUcAlarms: {
            $push: {
              $cond: [
                {
                  $and: [{ $eq: ['$controller', CONTROLLER_TYPE.uc] }, { $eq: ['$type', ALARM_TYPE.alarm] }],
                },
                '$$ROOT',
                null,
              ],
            },
          },
          activeUcAlerts: {
            $push: {
              $cond: [
                {
                  $and: [{ $eq: ['$controller', CONTROLLER_TYPE.uc] }, { $eq: ['$type', ALARM_TYPE.alert] }],
                },
                '$$ROOT',
                null,
              ],
            },
          },
          activeGwAlerts: {
            $push: {
              $cond: [
                {
                  $and: [{ $eq: ['$controller', CONTROLLER_TYPE.gw] }, { $eq: ['$type', ALARM_TYPE.alert] }],
                },
                '$$ROOT',
                null,
              ],
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          activeAlarms: {
            $filter: {
              input: '$activeAlarms',
              as: 'alarm',
              cond: { $ne: ['$$alarm', null] },
            },
          },
          activeAlerts: {
            $filter: {
              input: '$activeAlerts',
              as: 'alert',
              cond: { $ne: ['$$alert', null] },
            },
          },
          activeAlarmsCountLast24Hours: {
            $filter: {
              input: '$activeAlarmsCountLast24Hours',
              as: 'alarm24',
              cond: { $ne: ['$$alarm24', null] },
            },
          },
          activeGwAlarmsCountLast24Hours: {
            $filter: {
              input: '$activeGwAlarmsCountLast24Hours',
              as: 'alarmGw24',
              cond: { $ne: ['$$alarmGw24', null] },
            },
          },
          activeUcAlarms: {
            $filter: {
              input: '$activeUcAlarms',
              as: 'ucalarm',
              cond: { $ne: ['$$ucalarm', null] },
            },
          },
          activeUcAlerts: {
            $filter: {
              input: '$activeUcAlerts',
              as: 'ucalert',
              cond: { $ne: ['$$ucalert', null] },
            },
          },
          activeGwAlertsCount: {
            $filter: {
              input: '$activeGwAlerts',
              as: 'gwalert',
              cond: { $ne: ['$$gwalert', null] },
            },
          },
        },
      },
    ];

    return this.model.aggregate(aggregateRules);
  }

  async getAlarmsByAssetId({
    assetId,
    startDate,
    endDate,
  }: {
    assetId: string;
    startDate: number;
    endDate: number;
  }): Promise<AlarmBasicInterface[]> {
    return this.model
      .find(
        {
          assetId,
          status: ACTIVE_STATUS,
          activatedAt: {
            $gte: startDate,
            $lte: endDate,
          },
          resetAt: {
            $gte: startDate,
            $lte: endDate,
          },
        },
        {
          assetId: 1,
          serialNumber: 1,
          timestamp: 1,
          measureName: 1,
          measureValue: 1,
          createdAt: 1,
          resetAt: 1,
          activatedAt: 1,
          status: 1,
          code: 1,
          type: 1,
        },
      )
      .exec();
  }

  async getAlarmsByAssetIds({
    assetIds,
    startDate,
    endDate,
  }: {
    assetIds: string[];
    startDate: number;
    endDate: number;
  }): Promise<AlarmBasicInterface[]> {
    return this.model
      .find(
        {
          assetId: { $in: assetIds },
          status: ACTIVE_STATUS,
          activatedAt: {
            $gte: startDate,
            $lte: endDate,
          },
        },
        {
          assetId: 1,
          serialNumber: 1,
          timestamp: 1,
          measureName: 1,
          measureValue: 1,
          createdAt: 1,
          resetAt: 1,
          activatedAt: 1,
          status: 1,
          code: 1,
          type: 1,
          criticality: 1,
          possibleCause: 1,
          description: 1,
          resetType: 1,
          category: 1,
        },
      )
      .exec();
  }

  async getActiveAlarmAlertsByCountry({ country }: { country: string }): Promise<any> {
    const aggregateRules = [
      // Stage 1: Match the required filters
      {
        $match: {
          siteCountry: country,
          status: ACTIVE_STATUS,
          controller: CONTROLLER_TYPE.uc,
          resetAt: null,
          // Assuming you want to consider both "ALARM" and "ALERT" types in your result
          $or: [{ type: ALARM_TYPE.alarm }, { type: ALARM_TYPE.alert }],
        },
      },

      // Stage 2: Add a new field to calculate the time difference in hours between 'createdAt' and the current time
      {
        $addFields: {
          timeDifferenceInHours: {
            $divide: [
              { $subtract: [new Date(), { $toDate: '$createdAt' }] },
              3600000, // milliseconds in one hour
            ],
          },
        },
      },

      // Stage 3: Add a field to categorize documents based on the time difference into respective buckets
      {
        $addFields: {
          bucket: {
            $switch: {
              branches: [
                {
                  case: { $lte: ['$timeDifferenceInHours', 6] },
                  then: '0-6hrs',
                },
                {
                  case: { $and: [{ $gt: ['$timeDifferenceInHours', 6] }, { $lte: ['$timeDifferenceInHours', 12] }] },
                  then: '6-12hrs',
                },
                {
                  case: { $and: [{ $gt: ['$timeDifferenceInHours', 12] }, { $lte: ['$timeDifferenceInHours', 24] }] },
                  then: '12-24hrs',
                },
                {
                  case: { $and: [{ $gt: ['$timeDifferenceInHours', 24] }, { $lte: ['$timeDifferenceInHours', 48] }] },
                  then: '24-48hrs',
                },
                {
                  case: { $and: [{ $gt: ['$timeDifferenceInHours', 48] }, { $lte: ['$timeDifferenceInHours', 72] }] },
                  then: '48-72hrs',
                },
                {
                  case: { $gt: ['$timeDifferenceInHours', 72] },
                  then: '72+',
                },
              ],
              default: 'Unknown',
            },
          },
        },
      },

      // Stage 4: Group all documents together and count the occurrences of each bucket for both "ALARM" and "ALERT"
      {
        $group: {
          _id: null,
          '0-6hrsAlarms': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '0-6hrs'] }, { $eq: ['$type', ALARM_TYPE.alarm] }] }, 1, 0],
            },
          },
          '0-6hrsAlerts': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '0-6hrs'] }, { $eq: ['$type', ALARM_TYPE.alert] }] }, 1, 0],
            },
          },
          '6-12hrsAlarms': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '6-12hrs'] }, { $eq: ['$type', ALARM_TYPE.alarm] }] }, 1, 0],
            },
          },
          '6-12hrsAlerts': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '6-12hrs'] }, { $eq: ['$type', ALARM_TYPE.alert] }] }, 1, 0],
            },
          },
          '12-24hrsAlarms': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '12-24hrs'] }, { $eq: ['$type', ALARM_TYPE.alarm] }] }, 1, 0],
            },
          },
          '12-24hrsAlerts': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '12-24hrs'] }, { $eq: ['$type', ALARM_TYPE.alert] }] }, 1, 0],
            },
          },
          '24-48hrsAlarms': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '24-48hrs'] }, { $eq: ['$type', ALARM_TYPE.alarm] }] }, 1, 0],
            },
          },
          '24-48hrsAlerts': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '24-48hrs'] }, { $eq: ['$type', ALARM_TYPE.alert] }] }, 1, 0],
            },
          },
          '48-72hrsAlarms': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '48-72hrs'] }, { $eq: ['$type', ALARM_TYPE.alarm] }] }, 1, 0],
            },
          },
          '48-72hrsAlerts': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '48-72hrs'] }, { $eq: ['$type', ALARM_TYPE.alert] }] }, 1, 0],
            },
          },
          '72+Alarms': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '72+'] }, { $eq: ['$type', ALARM_TYPE.alarm] }] }, 1, 0],
            },
          },
          '72+Alerts': {
            $sum: {
              $cond: [{ $and: [{ $eq: ['$bucket', '72+'] }, { $eq: ['$type', ALARM_TYPE.alert] }] }, 1, 0],
            },
          },
        },
      },

      // Stage 5: Project the result in separate objects for Alarms and Alerts
      {
        $project: {
          _id: 0,
          alarmTrends: {
            zeroToSixHrs: '$0-6hrsAlarms',
            sixToTwelveHrs: '$6-12hrsAlarms',
            twelveToTwentyFourHrs: '$12-24hrsAlarms',
            TwentyFourToFortyEightHrs: '$24-48hrsAlarms',
            fortyEightToSeventyTwoHrs: '$48-72hrsAlarms',
            aboveSeventyTwoHrs: '$72+Alarms',
          },
          alertTrends: {
            zeroToSixHrs: '$0-6hrsAlerts',
            sixToTwelveHrs: '$6-12hrsAlerts',
            twelveToTwentyFourHrs: '$12-24hrsAlerts',
            TwentyFourToFortyEightHrs: '$24-48hrsAlerts',
            fortyEightToSeventyTwoHrs: '$48-72hrsAlerts',
            aboveSeventyTwoHrs: '$72+Alerts',
          },
        },
      },
    ];
    return this.model.aggregate(aggregateRules).exec();
  }
}
