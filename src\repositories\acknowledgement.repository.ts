import { ClientSession, FilterQuery, Model, Types } from 'mongoose';
import { AcknowledgementInterface, CaseSummaryInterface } from '../interfaces';
import { PageCriteria, FilterCriteria } from 'interfaces/pagination.interface';
import { createAuthQuery } from '../helpers/createAuthQuery';

export class AcknowledgementRepository {
  // eslint-disable-next-line
  constructor(private readonly model: Model<AcknowledgementInterface>) { }

  async findAll(): Promise<AcknowledgementInterface[]> {
    return this.model.find().exec();
  }

  async findManyBySiteId(sireId: string): Promise<AcknowledgementInterface[]> {
    return this.model.find({ siteId: sireId }).exec();
  }

  async getLastAcknowledgementsBySiteList(siteIds: string[], from: number): Promise<AcknowledgementInterface[]> {
    const acknowledges = await this.model.aggregate([
      {
        $match: {
          siteId: {
            $in: siteIds,
          },
          acknowledgedAt: { $gte: from },
        },
      },
      {
        $group: {
          _id: '$siteId',
          acknowledgedAt: {
            $max: '$acknowledgedAt',
          },
        },
      },
      {
        $sort: {
          acknowledgedAt: -1,
        },
      },
    ]);

    const filter = acknowledges.map((item) => {
      return { siteId: item._id, acknowledgedAt: item.acknowledgedAt };
    });

    if (!filter.length) {
      return [];
    }

    return this.model.find({ $or: filter }).exec();
  }

  async findById(_id: string): Promise<AcknowledgementInterface | null> {
    return this.model.findById(_id).exec();
  }

  async create(payload: any, session?: ClientSession): Promise<AcknowledgementInterface> {
    const [response] = await this.model.create([payload], null, { session });

    return response;
  }

  async update(_id: string, dto: any, session?: ClientSession): Promise<AcknowledgementInterface | null> {
    await this.model
      .updateOne(
        {
          _id: new Types.ObjectId(_id),
        },
        dto,
        { session, new: true },
      )
      .exec();

    return this.findById(_id);
  }

  async delete(_id: string, session?: ClientSession): Promise<AcknowledgementInterface | null> {
    return this.model.findByIdAndDelete(_id, { session }).exec();
  }

  async updateByCaseTransactionId(
    caseTransactionId: string,
    dto: any,
    session?: ClientSession,
  ): Promise<AcknowledgementInterface | null> {
    await this.model
      .updateOne(
        {
          caseTransactionId,
        },
        dto,
        { session, new: true },
      )
      .exec();

    return this.findByCaseTransactionId(caseTransactionId);
  }

  async findByCaseTransactionId(caseTransactionId: string): Promise<AcknowledgementInterface | null> {
    return this.model.findOne({ caseTransactionId }).exec();
  }

  async findByFilter(filter: any, projection?: any, page?: PageCriteria, authScopeFilter?: FilterCriteria[]): Promise<{ data: AcknowledgementInterface[]; totalCount: number }> {
    let match: FilterQuery<AcknowledgementInterface> = { ...filter };
    const acknowledgedStatus = "NO_NEEDED";
    const last24hours = +new Date(Date.now() - 24 * 60 * 60 * 1000);
    const sortOptions = {
      $addFields: {
        isNewCase: {
          $and: [
            {$ne: ["$status", acknowledgedStatus]},
            {$ne: [{ $ifNull: ["$caseId", null] }, null]},
            {$gte: ["$acknowledgedAt", last24hours]}
          ],
        },
      }
    };

    if (authScopeFilter?.length) {
      const authFilter = createAuthQuery<AcknowledgementInterface>(authScopeFilter);
      match = {
        $and: [match, ...authFilter],
      };
    }
    const pipeline: any[] = [
      { $match: match },
      sortOptions,
      {
        $sort: {
          isNewCase: -1,
          acknowledgedAt: -1
        }
      },
      { $skip: page?.offset ?? 0 },
      { $limit: page?.limit ?? 10 }];
  
    if (projection && Object.keys(projection).length > 0) {
      pipeline.push({ $project: {...projection, isNewCase: 1} });
    }
    const [data, totalCount] = await Promise.all([
      this.model.aggregate(pipeline).exec(),
      this.model.countDocuments(match).exec(),
    ]);
    return { 
      data,
      totalCount
    };
  }

  async countCaseSummary(authScopeFilter?: FilterCriteria[]): Promise<CaseSummaryInterface[]> {
    const closedCaseStatus = "Closed";

    let matchOpenCondition: any = {
      $and: [
        { caseId: { $exists: true, $ne: null } },
        { caseStatus: { $ne: closedCaseStatus } },        
      ]
    };

    let matchOpenRecentCondition: any = {
      $and: [
        { caseId: { $exists: true, $ne: null } },
        { acknowledgedAt: { $gte: +new Date(new Date().getTime() - 24 * 60 * 60 * 1000) } }
      ]
    };

  let matchResolvedRecentCondition: any = {
      $and: [
        { caseStatus: closedCaseStatus },
        { caseStatusUpdatedAt: { $gte: +new Date(new Date().getTime() - 24 * 60 * 60 * 1000) } }
      ]
    };

    if (authScopeFilter?.length) {
      const authFilter = createAuthQuery<AcknowledgementInterface>(authScopeFilter);
      matchOpenCondition = {
        $and: [matchOpenCondition, ...authFilter],
      };
      matchOpenRecentCondition = {
        $and: [matchOpenRecentCondition, ...authFilter],
      };
      matchResolvedRecentCondition = {
        $and: [matchResolvedRecentCondition, ...authFilter],
      };
    }

    return this.model.aggregate([
      {
        $match: matchOpenCondition
      },
      {
        $group: {
          _id: null, 
          totalOpenCases: { $sum: 1 }
        }
      },
      {
        $addFields: {
          totalOpenCases: { $ifNull: [{ $toInt: "$totalOpenCases" }, 0] }
        }
      },
      {
        $lookup: {
          from: "Acknowledgement",
          pipeline: [
            {
              $match: matchOpenRecentCondition
            },
            {
              $count: "openCasesLast24Hours"
            }
          ],
          as: "openCasesLast24Hours"
        }
      },
      {
        $unwind: {
          path: "$openCasesLast24Hours",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $addFields: {
          openCasesLast24Hours: { $ifNull: [{ $toInt: "$openCasesLast24Hours.openCasesLast24Hours" }, 0] }
        }
      },
      {
        $lookup: {
          from: "Acknowledgement",
          pipeline: [
            {
              $match: matchResolvedRecentCondition
            },
            {
              $count: "resolvedCasesLast24Hours"
            }
          ],
          as: "resolvedCasesLast24Hours"
        }
      },
      {
        $unwind: {
          path: "$resolvedCasesLast24Hours",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $addFields: {
          resolvedCasesLast24Hours: { $ifNull: [{ $toInt: "$resolvedCasesLast24Hours.resolvedCasesLast24Hours" }, 0] }
        }
      },
      {
        $project: {
          results: [
            {
              name: "Open Cases",
              count: { $ifNull: ["$totalOpenCases", 0] }
            },
            {
              name: "Recent Opened New",
              count: { $ifNull: ["$openCasesLast24Hours", 0] }
            },
            {
              name: "Recent Resolved",
              count: { $ifNull: ["$resolvedCasesLast24Hours", 0] }
            }
          ]
        }
      },
      {
        $unwind: {
          path: "$results",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $replaceRoot: { newRoot: "$results" }
      }
    ]);
  }
}
