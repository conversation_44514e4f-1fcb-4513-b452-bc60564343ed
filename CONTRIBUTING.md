# ZEPHYR-BACKEND-LIB-DOMAIN-ALARMS CONTRIBUTING GUIDE

## Contents

[TOC]

## Getting Started

Before we start, please, make sure you are familiar with the guidance in the [Contributing Guide Template](https://carrier-digital.atlassian.net/wiki/spaces/VER/pages/2661253179/Abound+CONTRIBUTING+Guide+Instructions+and+Template)

Provide an overview of this documentation. A sample is provided for reference here.

```
[This guide contains the necessary instructions for developers who will contribute to AHS. This Contributing Guide provides instructions for working in this repository. It also contains links to the architecture, an overview of the project structure, and details about any prerequisites required. For a more detailed overview and links to related documentation, see the [ReadMe file](https://carrier-digital.atlassian.net/wiki/spaces/VER/pages/2660598183/Abound+Repo+README+Template)].
```

### Prerequisites

* List all prerequisites in bullet form

#### Common
 * AWS-CLI
 * Node 16

#### Mac: 
 * Docker-Desktop v2


## Architecture

[ Solution Architecture on Confluence ](link)


## Project Structure

- Describe project-tree structure, purpose of main files, how and where new files should be placed. 
- Use bullets if listing out separate items.


## Instructions

Any instructions needed by both contributing and consuming developers will be located in the Reference folder and linked from this section.

```
This section should contain instructions and context if necessary. 

- If there are instructions that are identical for both consuming and contributing developers:
     - Create a Reference folder in the Repo 
     - Create separate markdown files for each instruction. 
     - Under the instruction heading below (example: How to Configure), link to the new markdown file.   
- If this is a backend template, make sure the repo describes how to work on the template (or points to the backend template ReadMe) and how to use it because the audience may come directly to a specific template ReadMe vs the Template Repo ReadMe.

```

### How to Install

1. Step 1 to install
 * [MAC] Install mac-specific app
 * [WIN] Install windows-specific app
 * [LINUX] Install linux-specific app
2. Step 2 to install
3. Etc


### How to Configure

1. Step 1 to Configure
2. Step 2 to Configure
3. Etc


### How to Develop

1. Step 1 to Develop
2. Step 2 to Develop
3. Etc


### How to Format

1. Step 1 to Formatting
2. Step 2 to Formatting
3. Etc


### How to Deploy from local to Personal Stage

1. Step 1 to Deploy
2. Step 2 to Deploy
3. Etc

### How to Deploy to Dev/Prod

1. Step 1 to Deploy
2. Step 2 to Deploy
3. Etc


```
OR link to Confluence topic.

```