!jest.config.js
*.d.ts
node_modules
dist

# CDK asset staging directory
.cdk.staging
cdk.out
.aws-sam
cdk.context.json

# NPM configurations
.npmrc
.yarnrc

# misc
coverage/
npm-debug.log
yarn-error.log

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# compiled output
cdk.out/
dist/
.build/

# dependencies
node_modules/
dist/

# IDEs and editors
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.idea/