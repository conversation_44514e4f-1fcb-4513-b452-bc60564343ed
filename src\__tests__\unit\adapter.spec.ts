import { SecretManager } from '../../services';
import { MongoDBAdapter } from '../../adapters';

const dbAdapter = new MongoDBAdapter();

describe(`When Connect to MongoDB Atlas`, () => {
  afterEach(async () => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    jest.restoreAllMocks();
  });

  describe(`And Secrets Data is correct`, () => {
    it('Should create new connection', async () => {
      const connection = await dbAdapter.connect();

      expect(typeof connection).toEqual(typeof import('mongoose'));
    });

    it('Should close connection after', async () => {
      await dbAdapter.connect();
      await dbAdapter.closeConnection();
    });
  });

  describe(`And Secrets Data is empty`, () => {
    it('Should log error to console', async () => {
      jest.spyOn(SecretManager.prototype, 'getSecretValue').mockImplementationOnce(async () => {
        return {};
      });
      process.env.DB_USER = '';

      let errorMessage;

      try {
        await dbAdapter.connect();
      } catch (error) {
        errorMessage = error instanceof Error ? error.message : 'Untraceable error';
      }

      expect(errorMessage).toEqual('DB connection error: Unexpected end of JSON input');
    });
  });
});
