jest.mock('@aws-sdk/client-secrets-manager', () => {
  const originalModule = jest.requireActual('@aws-sdk/client-secrets-manager');

  return {
    ...originalModule,
    SecretsManagerClient: jest.fn().mockImplementation(() => {
      return {
        send: jest.fn(() => {
          return {
            SecretString: JSON.stringify({
              username: 'test',
              password: 'test',
            }),
          };
        }),
      };
    }),
  };
});

jest.mock('mongoose', () => {
  const originalModule = jest.requireActual('mongoose');

  return {
    ...originalModule,
    startSession: jest.fn(() => {
      return {
        endSession: jest.fn(),
      };
    }),
    connect: jest.fn(() => {
      return {
        close: jest.fn(),
      };
    }),
  };
});
