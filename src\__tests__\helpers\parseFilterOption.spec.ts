import { parseFilterOption } from '../../helpers';

const testData = [
  {
    field: 'isMuted',
    option: 'null',
    expected: null,
  },
  {
    field: 'isMuted',
    option: 'false',
    expected: false,
  },
  {
    field: 'resetAt',
    option: '123456789',
    expected: 123456789,
  },
  {
    field: 'criticality',
    option: '1',
    expected: 1,
  },
  {
    field: 'code',
    option: '156',
    expected: '156',
  },
  {
    field: 'fullCode',
    option: 'null',
    expected: null,
  },
];

describe('parseFilterOption', () => {
  it.each(testData)(
    "should return value $expected with expected type for '$field' the field and string option '$option'",
    ({ field, expected, option }) => {
      const result = parseFilterOption(field, option);
      expect(result).toEqual(expected);
    },
  );
});
