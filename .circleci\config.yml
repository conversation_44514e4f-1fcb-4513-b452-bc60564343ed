# Generated by this commit hash: 76394eac45539205fc20d029a91ea08b767cdf2f
version: 2.1
parameters:
  environment:
    type: string
    default: dev
  main-branch:
    type: string
    default: master
  channel-name:
    type: string
    default: 'add a valid slack channel'
    description: |
      Setting an invalid value will ensure that no messages are sent and the attempts to send messages fail gracefully
      (without interrupting the remainder of the pipeline)
      Do add a valid slack channel if notification is required.
  workflow_controller:
    default: false
    type: boolean
  repository-name:
    type: string
    default: zephyr-backend-lib-domain-alarms
  premerge-build:
    type: boolean
    default: false
  aws_codeartifact_partition:
    type: string
    default: 'aws'
    description: >
      AWS Partition used to login to CodeArtifact
  aws_codeartifact_account_id:
    type: string
    default: '************'  # CarrierIO Prod
    description: >
      AWS Account used to login to CodeArtifact
  aws_codeartifact_region_name:
    type: string
    default: 'us-east-1'
    description: >
      AWS Region used to login to CodeArtifact
  aws_partition:
    type: string
    default: 'aws'
    description: >
      AWS Partition that will be deployed t
orbs:
  aws-auth: cardig/aws-auth@1.2.1
  awsauth: cardig/awsauth@2
  aws-cli: circleci/aws-cli@2.0.6
  aws-s3: circleci/aws-s3@3.0.0
  aws-ssm: cardig/aws-ssm@1.1.0
  jira: cardig/priv-jira@3.0.0
  priv-aws-auth: cardig/priv-aws-auth@0.2.1
  priv-ca: cardig/priv-code-artifact@2.0.1
  priv-cache: cardig/priv-cache@1.0.0
  priv-code-analysis: cardig/priv-code-analysis@2.0.0
  priv-synopsis: cardig/priv-synopsis@2
  priv-workflow: cardig/priv-workflow@4.0.0
  semver: cardig/semver@1.0.1
  slack: circleci/slack@4.2.0
executors:
  default-executor: &default-executor
    docker:
      - image: cimg/node:18.20.7-browsers
  scan-executor:
    <<: *default-executor
  sonar-executor:
    <<: *default-executor
  build-executor:
    <<: *default-executor
commands:
  fail-tag-deployment:
    steps:
      - when:
          condition: << pipeline.git.tag >>
          steps:
            - run:
                command: |
                  new_tag="<< pipeline.git.tag >>-failed"
                  echo "Pushing Tag :     $new_tag"
                  git config --global user.email "<EMAIL>"
                  git config --global user.name "circleci"
                  git tag -a $new_tag $CIRCLE_SHA1 -m 'tag pushed from circleci'
                  git push origin $new_tag
                  echo "Tag pushed!"
                name: Pushed Failed Tag
                when: on_fail
                working_directory: .circleci
            - run:
                command: |
                  echo "Deleting prior tag"
                  git push --delete origin << pipeline.git.tag >>
                name: Delete Failed Trigger Tag
                when: on_fail
                working_directory: .circleci
  ca-deploy:
    parameters:
      environment:
        type: string
      npm-path:
        type: string
        default: .
    steps:
      - when:
          condition:
            not: <<pipeline.git.tag>>
          steps:
            - priv-ca/ca-deploy-branch:
                npm-path: <<parameters.npm-path>>
                pre-publish:
                  - run:
                      command: |
                        cd <<parameters.npm-path>>
                        yarn build
                      name: Prepublish Steps
                revision: <<pipeline.git.revision>>
                run-publish:
                  - run:
                      command: |
                        cd <<parameters.npm-path>>
                        npm publish
                      name: Publish Package
      - when:
          condition:
            not: <<pipeline.git.branch>>
          steps:
            - priv-ca/ca-deploy-tag:
                environment: <<parameters.environment>>
                npm-path: <<parameters.npm-path>>
                pre-publish:
                  - run:
                      command: |
                        cd <<parameters.npm-path>>
                        yarn build
                      name: Prepublish Steps
                run-publish:
                  - priv-ca/hotfix-publish:
                      npm-path: <<parameters.npm-path>>
                      environment: <<parameters.environment>>
jobs:
  yarn-check-types:
    executor: <<parameters.executor>>
    parameters:
      env:
        type: string
      executor:
        type: executor
        default: build-executor
    steps:
      - checkout
      - priv-workflow/job-prep
      - priv-cache/cached-package-install
      - run: yarn check-types
      - when:
          condition:
            not:
              equal: ["add a valid slack channel", <<pipeline.parameters.channel-name>>]
          steps:
            - slack/notify:
                event: fail
                mentions: <!here>
                channel: <<pipeline.parameters.channel-name>>
                template: basic_fail_1
  yarn-lint:
    executor: <<parameters.executor>>
    parameters:
      env:
        type: string
      executor:
        type: executor
        default: build-executor
    steps:
      - checkout
      - priv-workflow/job-prep
      - priv-cache/cached-package-install
      - run: yarn lint --max-warnings 0
      - when:
          condition:
            not:
              equal: ["add a valid slack channel", <<pipeline.parameters.channel-name>>]
          steps:
            - slack/notify:
                event: fail
                mentions: <!here>
                channel: <<pipeline.parameters.channel-name>>
                template: basic_fail_1
  yarn-test:
    executor: build-executor
    parameters:
      env:
        type: string
    steps:
      - checkout
      - priv-workflow/job-prep
      - priv-cache/cached-package-install
      - run: yarn test
      - slack/notify:
          event: fail
          mentions: <!here>
          channel: <<pipeline.parameters.channel-name>>
          template: basic_fail_1
  #checks if updating dist-tag and/or publishing hotfix will downgrade current upper env dist-tags; also caches branch name on non main branch runs for pr merge
  ca-prep:
    executor: aws-cli/default
    parameters:
      environment:
        type: string
    steps:
      - checkout
      - priv-workflow/job-prep
      - when:
          condition:
            equal:
              - <<pipeline.parameters.main-branch>>
              - <<pipeline.git.branch>>
          steps:
            - priv-ca/validate-branch-name
      - when:
          condition:
            and:
              - not: << pipeline.git.tag >>
              - not:
                  equal:
                    - << pipeline.parameters.main-branch >>
                    - << pipeline.git.branch >>
          steps:
            - priv-ca/cache-branch-name
      - when:
          condition:
            not: << pipeline.git.branch >>
          steps:
            - priv-ca/prevent-dist-tag-downgrade:
                environment: <<parameters.environment>>
                channel-name: <<pipeline.parameters.channel-name>>
      - when:
          condition:
            not:
              equal: ["add a valid slack channel", <<pipeline.parameters.channel-name>>]
          steps:
            - slack/notify:
                event: fail
                mentions: <!here>
                channel: <<pipeline.parameters.channel-name>>
                template: basic_fail_1
  build-and-publish:
    executor: build-executor
    parameters:
      environment:
        type: string
      npm-path:
        type: string
        default: .
    steps:
      - checkout
      - priv-workflow/job-prep
      - priv-cache/cached-package-install
      - ca-deploy:
          environment: <<parameters.environment>>
          npm-path: <<parameters.npm-path>>
      - when:
          condition:
            not:
              equal: ["add a valid slack channel", <<pipeline.parameters.channel-name>>]
          steps:
            - slack/notify:
                event: fail
                mentions: <!here>
                channel: <<pipeline.parameters.channel-name>>
                template: basic_fail_1
workflows:
  #dev deployment
  branch-deployer:
    jobs:
      - priv-workflow/check-pr:
          name: Check PR Status
          filters:
            branches:
              ignore:
                - <<pipeline.parameters.main-branch>>
      - ca-prep: &ca-prep-step
          name: CA Prep
          environment: dev
          context:
            - SLACK
          pre-steps: &steps-workspace-auth
            - checkout
            - awsauth/oidc-authentication:
                aws-account-id: << pipeline.parameters.aws_codeartifact_account_id >>
                aws-partition: << pipeline.parameters.aws_codeartifact_partition >>
                aws-region-name: << pipeline.parameters.aws_codeartifact_region_name >>
            - priv-aws-auth/ca-login:
                domain: carrier
                repository: '@carrier-io'
                domain_owner: '************'
            - priv-aws-auth/ca-login:
                domain: carrier
                repository: '@carrier-io-insights'
                domain_owner: '************'
            - priv-aws-auth/ca-login:
                domain: carrier
                repository: '@carrier'
                domain_owner: '************'
            - priv-aws-auth/ca-login:
                domain: carrier
                repository: npm-store
                domain_owner: '************'
            - priv-aws-auth/ca-login:
                domain: carrier
                repository: '@zephyr'
                domain_owner: '************'
            - priv-aws-auth/ca-login:
                domain: carrier
                repository: '@abound'
                domain_owner: '************'
      - priv-code-analysis/scan: &sonar-scan
          name: Sonar Scan (With Coverage Reports)
          executor: sonar-executor
          resource-class: medium
          context:
            - SONAR
            - SLACK
          scan-prep:
            - priv-cache/cached-package-install
          channel-name: << pipeline.parameters.channel-name >>
          pre-steps:
            - *steps-workspace-auth
      - yarn-lint:
          name: Lint dev
          context:
            - SLACK
          env: dev
          pre-steps:
            - *steps-workspace-auth
      - yarn-test:
          name: yarn test
          context:
            - SLACK
          env: dev
          pre-steps:
            - *steps-workspace-auth
      - yarn-check-types:
          name: yarn check-types
          context:
            - SLACK
          env: dev
          pre-steps:
            - *steps-workspace-auth
      - priv-synopsis/blackduck-scan:
          name: Black Duck Scan
          executor: scan-executor
          context:
            - SYNOPSIS_BLACKDUCK
            - SLACK
          filters:
            branches:
              only:
                - <<pipeline.parameters.main-branch>>
          channel-name: <<pipeline.parameters.channel-name>>
          scan-prep:
            - priv-cache/cached-package-install
          pre-steps:
            - *steps-workspace-auth
      - priv-synopsis/polaris-scan:
          name: Polaris Scan
          executor: scan-executor
          context:
            - SYNOPSIS_POLARIS
            - SLACK
          filters:
            branches:
              only:
                - <<pipeline.parameters.main-branch>>
          channel-name: <<pipeline.parameters.channel-name>>
          scan-prep:
            - priv-cache/cached-package-install
          no-output-timeout: 60m
          pre-steps:
            - *steps-workspace-auth
      - build-and-publish:
          name: Deploy dev
          context: SLACK
          environment: dev
          pre-steps:
            - *steps-workspace-auth
          filters:
            branches:
              only:
                - <<pipeline.parameters.main-branch>>
          requires:
            - CA Prep
      - jira/get-and-post: &jira-post
          name: Update Jira dev
          deploy-environment: dev
          commit-history-type: branch
          filters:
            branches:
              only:
                - <<pipeline.parameters.main-branch>>
          requires:
            - Deploy dev
    when:
      and:
        - not: << pipeline.git.tag >>
        - not: << pipeline.parameters.premerge-build >>
  premerge-deployer:
    jobs:
      - priv-workflow/sync-main:
          name: Merge in <<pipeline.parameters.main-branch>>
          main-branch: <<pipeline.parameters.main-branch>>
          pre-steps:
            - *steps-workspace-auth
      - priv-code-analysis/scan:
          <<: *sonar-scan
          requires:
            - Merge in <<pipeline.parameters.main-branch>>
    when:
      and:
        - not: << pipeline.git.tag >>
        - << pipeline.parameters.premerge-build >>
  tag-router:
    jobs:
      - priv-ca/tag-routing: &tag-routing
          context: SLACK
          filters:
            tags:
              only: /rc/
          name: Handle RC Tag
          environment: qa
          channel-name: <<pipeline.parameters.channel-name>>
          pipeline-number: <<pipeline.number>>
          main-branch: <<pipeline.parameters.main-branch>>
          route: tag
          pre-steps:
            - *steps-workspace-auth
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /hotfix-(qa|preprod|prod)/
          name: Handle Hotfix Tag
          environment: <<pipeline.parameters.environment>>
          route: hotfix-tag
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
          name: Handle QA Tag
          environment: qa
          route: controller
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-preprod\+([0-9]*)\.([a-f0-9]){7}/
          name: Handle PreProd Tag
          environment: preprod
          route: controller
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-prod\+([0-9]*)\.([a-f0-9]){7}/
          name: Handle Prod Tag
          environment: prod
          route: controller
    when:
      and:
        - << pipeline.git.tag >>
        - not: << pipeline.parameters.workflow_controller >>
  tag-deployer:
    jobs:
      - ca-prep:
          <<: *ca-prep-step
          environment: <<pipeline.parameters.environment>>
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-<< pipeline.parameters.environment >>.*/
      - build-and-publish:
          name: Deploy to <<pipeline.parameters.environment>>
          environment: <<pipeline.parameters.environment>>
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-<< pipeline.parameters.environment >>.*/
          context:
            - SLACK
          pre-steps:
            - *steps-workspace-auth
          requires:
            - CA Prep
      - jira/get-and-post:
          name: Update Jira <<pipeline.parameters.environment>>
          commit-history-type: tag
          deploy-environment: <<pipeline.parameters.environment>>
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-<< pipeline.parameters.environment >>.*/
          requires:
            - Deploy to <<pipeline.parameters.environment>>
      - slack/on-hold: &slack-on-hold
          name: Preprod Approval Notification
          context: SLACK
          mentions: <!here>
          branch_pattern: <<pipeline.parameters.main-branch>>
          channel: <<pipeline.parameters.channel-name>>
          requires:
            - Deploy to <<pipeline.parameters.environment>>
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
      - approval:
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
          name: Promote to Preprod
          requires:
            - Preprod Approval Notification
          type: approval
      - priv-ca/tag-routing:
          <<: *tag-routing
          context: SLACK
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-qa\+([0-9]*)\.([a-f0-9]){7}/
          name: Push Preprod Tag
          environment: preprod
          requires:
            - Promote to Preprod
      - slack/on-hold:
          <<: *slack-on-hold
          name: Prod Approval Notification
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-preprod\+([0-9]*)\.([a-f0-9]){7}/
          requires:
            - Deploy to <<pipeline.parameters.environment>>
      - approval:
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-preprod\+([0-9]*)\.([a-f0-9]){7}/
          name: Promote to Production
          requires:
            - Prod Approval Notification
          type: approval
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-preprod\+([0-9]*)\.([a-f0-9]){7}/
          name: Push Prod Tag
          environment: prod
          requires:
            - Promote to Production
      - priv-ca/tag-routing:
          <<: *tag-routing
          filters:
            tags:
              only: /([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])\.([1-9][0-9]+|[0-9])-prod\+([0-9]*)\.([a-f0-9]){7}/
          name: Push Release Tag
          environment: release
          requires:
            - Deploy to << pipeline.parameters.environment >>
    when:
      and:
        - << pipeline.git.tag >>
        - << pipeline.parameters.workflow_controller >>
