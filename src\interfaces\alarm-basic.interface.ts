import { Document, Types } from 'mongoose';

export interface AlarmBasicInterface extends Document<Types.ObjectId> {
  assetId: string;
  timestamp: number;
  measureName: string;
  measureValue: boolean;
  createdAt: number;
  status: string;
  code: string | null;
  type: string | null;
  resetType: string | null;
  activatedAt: number | null;
  resetAt: number | null;
  serialNumber: string | null;
}
