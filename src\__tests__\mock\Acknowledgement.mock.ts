import { ObjectId } from 'mongodb';

export const AcknowledgementMockData = [
  {
    status: 'ACKNOWLEDGED',
    alarmIds: [new ObjectId('64a2b22c8bc04ab8b3bfabd9')],
    siteId: '7dfb3865-eb64-4e92-88e3-e5cd61004d5c',
    acknowledgedBy: {
      userId: '00u1db5zltptGSANh0h8',
      email: '<EMAIL>',
      fullName: 'Dev Zephyr',
      _id: new ObjectId('64a2c8fea9b7899d43693c20'),
    },
    acknowledgedAt: 1688389886237,
    __v: 0,
    caseId: '00719878',
    caseStatus: 'Accepted',
    link: 'https://carrier--ahpdev.sandbox.my.salesforce.com/500KT0000010kJnYAI',
    workOrderId: 'WO-03057278',
    workOrderLink: 'https://carrier--ahpdev.sandbox.my.salesforce.com/a4iKT0000004DJVYA2',
    workOrderStatus: 'Ready for Dispatch',
    workOrderCreatedAt: 1716380691000,
    siteRegion: 'India'
  },
];

export const NewAcknowledgementMockData = {
  siteId: 'b92778a1-79fd-472d-a87b-d3eb808b2b23',
  acknowledgedAt: Date.now(),
  status: 'ACKNOWLEDGED',
};
