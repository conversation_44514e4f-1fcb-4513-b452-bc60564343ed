import { Document, Types } from 'mongoose';
import { CaseTypeEnum } from '../enums';

export interface AcknowledgedByInterface extends Document<Types.ObjectId> {
  userId: string;
  email?: string;
  fullName?: string;
}

export interface AcknowledgementInterface extends Document<Types.ObjectId> {
  status: string;
  alarmIds: string[];
  acknowledgedAt: number;
  acknowledgedBy: AcknowledgedByInterface;
  link?: string | null;
  siteId: string;
  caseTransactionId?: string | null;
  caseId?: string | null;
  caseStatus?: string | null;
  workOrderId?: string | null;
  workOrderStatus?: string | null;
  workOrderLink?: string | null;
  workOrderCreatedAt?: number | null;
  siteName?: string | null;
  caseType?: CaseTypeEnum;
  siteRegion?: string | null;
  siteCountry?: string | null;
  siteMarket?: string | null;
  siteCommercialTerritory?: string | null;
  workOrderStatusUpdatedAt?: number | null;
  caseStatusUpdatedAt?: number | null;
}

export interface CaseSummaryInterface extends Document<Types.ObjectId> {
  name: string;
  count: number;
}
