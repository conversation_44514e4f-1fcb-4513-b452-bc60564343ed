{"root": true, "ignorePatterns": ["node_modules/", "dist/", "coverage/", "tools/", "cdk.out/"], "plugins": ["@typescript-eslint", "sonarjs", "prettier", "jest"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "tsconfigRootDir": "tsconfig.json"}, "env": {"jest/globals": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier", "airbnb-base", "plugin:sonarjs/recommended", "plugin:import/typescript", "plugin:import/errors", "plugin:import/warnings", "plugin:prettier/recommended"], "rules": {"no-underscore-dangle": "off", "no-restricted-syntax": "off", "no-unused-vars": "off", "no-undef": "off", "no-shadow": "off", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index", "object"]}], "class-methods-use-this": "off", "import/ignore": "off", "import/prefer-default-export": "off", "import/extensions": "off", "import/no-extraneous-dependencies": "off", "no-use-before-define": "off", "no-dupe-class-members": "off", "@typescript-eslint/no-use-before-define": ["error"], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "ignoreRestSiblings": true}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-shadow": "off", "@typescript-eslint/explicit-member-accessibility": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-parameter-properties": "off", "prettier/prettier": ["warn", {"trailingComma": "all", "tabWidth": 2, "semi": true, "singleQuote": true, "bracketSpacing": true, "eslintIntegration": true, "printWidth": 120}]}, "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx", ".js", ".jsx"]}, "import/resolver": {"typescript": {"project": "tsconfig.json", "alwaysTryTypes": true}}}}