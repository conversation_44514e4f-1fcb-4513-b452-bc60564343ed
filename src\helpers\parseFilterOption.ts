import { Types } from 'mongoose';

const ALARM_NUMERIC_FIELDS = ['resetAt', 'criticality'];
const ALARM_BOOLEAN_FIELDS = ['isMuted'];
const ALARM_OBJECTID_FIELDS = ['_id'];

// filter option can contain null, boolean, number value as a string for some fields
export const parseFilterOption = (field: string, option: string) => {
  const result = option !== 'null' ? option : null;

  if (result) {
    if (ALARM_NUMERIC_FIELDS.includes(field)) {
      return Number(result);
    }
    if (ALARM_BOOLEAN_FIELDS.includes(field) && ['true', 'false'].includes(result)) {
      return JSON.parse(result) as boolean;
    }
    if (ALARM_OBJECTID_FIELDS.includes(field)) {
      return new Types.ObjectId(result);
    }
  }

  return result;
};
