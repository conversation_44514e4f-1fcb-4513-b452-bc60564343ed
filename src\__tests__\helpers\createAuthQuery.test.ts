import type { FilterCriteria } from 'interfaces/pagination.interface';
import { createAuthQuery } from '../../helpers/createAuthQuery';

describe('createAuthQuery', () => {
  it('should return empty array for empty filter', () => {
    expect(createAuthQuery([])).toEqual([]);
  });

  it('should handle non-exclusive filters', () => {
    const filter: FilterCriteria[] = [
      { field: 'siteCountry', options: ['USA', 'Canada'] },
      { field: 'siteMarket', options: ['New York', 'North East'] },
    ];
    const result = createAuthQuery(filter);
    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({
      siteCountry: { $in: ['USA', 'Canada'] },
      siteMarket: { $in: ['New York', 'North East'] },
    });
  });

  it('should handle exclusive series filter', () => {
    const filter: FilterCriteria[] = [{ field: 'series', options: ['23', '(MC|UC)'] }];
    const result = createAuthQuery(filter);
    expect(result).toHaveLength(1);
    expect(result[0].$or).toBeDefined();
    expect(result[0].$or?.[0].series).toMatchObject({ $eq: null });
    expect(result[0].$or?.[1].series.$in).toHaveLength(2);
    expect(result[0].$or?.[1].series.$in[1].test('UC132')).toEqual(true);
    expect(result[0].$or?.[1].series.$in[0].test('23 Series')).toEqual(true);
    expect(result[0].$or?.[1].series.$in[0].test('30SERIES')).toEqual(false);
  });

  it('should handle both exclusive and non-exclusive filters', () => {
    const filter: FilterCriteria[] = [
      { field: 'series', options: ['23'] },
      { field: 'siteMarket', options: ['New York'] },
    ];
    const result = createAuthQuery(filter);
    expect(result).toHaveLength(2);
    expect(result[0]).toMatchObject({ siteMarket: { $in: ['New York'] } });
    expect(result[1].$or).toBeDefined();
    expect(result[1].$or?.[0].modelNumber.$in[0].test('23RXV')).toEqual(true);
  });

  it('should convert string "null" to null in non-exclusive filters', () => {
    const filter: FilterCriteria[] = [{ field: 'siteMarket', options: ['null', 'New York'] }];
    const result = createAuthQuery(filter);
    expect(result[0].siteMarket.$in).toContain(null);
    expect(result[0].siteMarket.$in).toContain('New York');
  });
});
