# ZEPHYR-BACKEND-LIB-DOMAIN-ALARMS README


## Table of contents
[TOC]

     
     
## Overview  
This repository contains the AHP Alarm Library, which provides functionalities related to handling alarms in the AHP system.


### Repository Documentation

   
   
| Markdown Link      | Description                                                                 |
| ------------------ | --------------------------------------------------------------------------- |
| Contributing Guide | Contains the documentation for developers making changes in the repository. |
| Docs ReadMe        | Contains Generated Markdown for Classes, Enums, etc                         |

### Relevant Confluence Documentation  

| Confluence Page                                                                                               | Description                                       |
| ------------------------------------------------------------------------------------------------------------- | ------------------------------------------------- |
| [Node Domain Confluence Documentation](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/2144502050) | Node Domain core Confluence pages.                |
| [AHP Glossary](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/2455339246/AHP+Glossary)         | Confluence Glossary for AHP Terms and Definitions |

## Architecture
- [Node domain architecture](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/2144763954/v1.10.0+Node+Domain+Architecture), most recent node domain architecture on carrier.io space;
- [AHP architecture overview](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/2250113504/Architectural+Blueprint+Roadmap+High+Level+Design), overarching architecture on AHP as a whole.


## Usage

### Installation

To install the library, you can use npm or yarn: 
```bash
bash npm install @zephyr/backend-lib-domain-alarms 
# or 
yarn add @zephyr/backend-lib-domain-alarms
```

## Modules


### Adapters 
MongoDBAdapter
The MongoDBAdapter module provides functionality to connect to MongoDB databases.

### Enums

This module contains enumeration files defining constant values.
- **AcknowledgementEnum**: Enumerates acknowledgement statuses.
- **STAGE**: Enumerates environment stages.
### Errors
The Errors module contains error handling files.
- **DatabaseError**: Represents errors related to database operations.

### Helpers
The Helpers module contains utility/helper files.
- **parseFilterOption**: Parses filter options for alarm data.

### Interfaces
The Interfaces module contains interface files defining data structures.
- **AcknowledgedByInterface**: Defines the structure of the entity acknowledging an alarm.
- **AcknowledgementInterface**: Defines the structure of an alarm acknowledgement.
- **AlarmMetadataInterface**: Defines the structure of metadata associated with alarms.
- **AlarmInterface**: Defines the structure of an alarm.
- **PageCriteria**: Defines criteria for pagination.
- **OrderCriteria**: Defines criteria for ordering.
- **FilterCriteria**: Defines criteria for filtering.

### Repositories
The Repositories module contains repository files responsible for interacting with data storage.
- **AcknowledgementModel**: Represents the model for interacting with acknowledgement data in the database.
- **AlarmMetadata**: Represents the model for interacting with alarm metadata data in the database.
- **AlarmModel**: Represents the model for interacting with alarm data in the database.

### Schemas
The Schemas module contains schema files defining data structures.
- **acknowledgedBySchema**: Defines the schema for the entity acknowledging an alarm.
- **AcknowledgementSchema**: Defines the schema for an alarm acknowledgement.
- **AlarmMetadataSchema**: Defines the schema for alarm metadata.
- **AlarmSchema**: Defines the schema for an alarm.


### Services
The Services module contains aws service functions.
- **SecretManager**: Provides functionality to retrieve secrets from AWS Secrets Manager.

## Examples
Here's an example of how you can use the MongoDB adapter:

```typescript
import { MongoDBAdapter } from '@zephyr/backend-lib-domain-alarms/adapters'; 

const mongodbAdapter = new MongoDBAdapter(); 

mongodbAdapter.connect() 
	.then((connection) => {console.log('Connected to MongoDB'); 
	}) 
	.catch((error) => { console.error('Error connecting to MongoDB:', error); 
	});
```

