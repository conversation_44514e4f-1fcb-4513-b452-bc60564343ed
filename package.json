{"name": "@zephyr/backend-lib-domain-alarms", "version": "0.90.4", "description": "AHP Alarm Library", "private": false, "license": "MIT", "files": ["/dist"], "types": "./dist/index.d.ts", "main": "./dist/index.js", "engines": {"node": ">=14"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.338.0", "@tsconfig/node20": "^20.1.2", "mongodb": "~6.14.2", "mongoose": "^8.12.1", "npm-run-all": "^4.1.5", "reflect-metadata": "^0.1.13", "ts-jest": "^29.1.0", "ts-jest-resolver": "^2.0.1", "tsconfig": "^7.0.0"}, "scripts": {"ca:login": "sh cicd/scripts/rc.sh", "prepublish": "yarn build", "build:drop": "rimraf ./dist", "build:make": "tsc --target es2020 --module commonjs", "build": "run-s build:*", "test": "jest --runInBand --passWithNoTests --config ./jest.config.unit.js --coverage", "lint": "tsc --noEmit && eslint --ext .ts ./src --fix", "check-types": "tsc --noEmit", "prepare": "husky install"}, "devDependencies": {"@types/jest": "^29.5.1", "@typescript-eslint/eslint-plugin": "^5.59.9", "@typescript-eslint/parser": "^5.59.9", "dotenv": "^16.0.3", "eslint": "^8.42.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-sonarjs": "^0.19.0", "husky": "^8.0.3", "jest": "^29.5.0", "prettier": "^2.8.8", "typescript": "^5.0.4", "mongodb-memory-server": "^9.2.0"}}