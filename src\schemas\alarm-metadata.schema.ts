import mongoose, { Schema, models } from 'mongoose';
import { AlarmMetadataInterface } from 'interfaces/alarm-metadata.interface';

const AlarmMetadataSchema = new Schema({
  alarmId: { type: String },
  alarmCode: { type: String },
  category: { type: String },
  chillerModel: { type: String },
  controllerType: { type: String },
  level: { type: Number },
  possibleCause: { type: String },
  resetType: { type: String },
  text: { type: String },
  type: { type: String },
  lookupKey: { type: String },
  translations: {
    ja_JP: {
      possibleCause: { type: String },
      text: { type: String },
    },
  },
});

export const AlarmMetadata =
  models.AlarmMetadata || mongoose.model<AlarmMetadataInterface>('AlarmMetadata', AlarmMetadataSchema, 'AlarmMetadata');
