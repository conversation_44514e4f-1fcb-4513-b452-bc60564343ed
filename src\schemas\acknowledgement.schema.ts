import mongoose, { Schema, models } from 'mongoose';
import { AcknowledgementInterface } from 'interfaces';
import { CaseTypeEnum } from '../enums';
import { AlarmModel } from './alarm.schema';

export const acknowledgedBySchema = new Schema({
  userId: { type: String, required: true },
  email: { type: String, required: false },
  fullName: { type: String, required: false },
});

const AcknowledgementSchema = new Schema({
  status: { type: String, required: true },
  alarmIds: [
    {
      type: Schema.Types.ObjectId,
      ref: AlarmModel,
    },
  ],
  link: { type: String, required: false },
  siteId: { type: String, required: true },
  acknowledgedBy: acknowledgedBySchema,
  acknowledgedAt: {
    type: Number,
    required: true,
    index: true,
    allowNull: true,
  },
  caseId: { type: String, required: false },
  caseStatus: { type: String, required: false },
  workOrderId: { type: String, required: false },
  workOrderStatus: { type: String, required: false },
  workOrderLink: { type: String, required: false },
  caseTransactionId: { type: String, required: false },
  workOrderCreatedAt: { type: Number, required: false, allowNull: true },
  siteName: { type: String, required: false },
  siteRegion: { type: String, required: false },
  siteCountry: { type: String, required: false },
  siteMarket: { type: String, required: false },
  siteCommercialTerritory: { type: String, required: false },
  caseType: { type: String, enum: Object.values(CaseTypeEnum), required: false },
  caseStatusUpdatedAt: { type: Number, required: false, allowNull: true },
  workOrderStatusUpdatedAt: { type: Number, required: false, allowNull: true },
});

export const AcknowledgementModel =
  models.Acknowledgement ||
  mongoose.model<AcknowledgementInterface>('Acknowledgement', AcknowledgementSchema, 'Acknowledgement');
