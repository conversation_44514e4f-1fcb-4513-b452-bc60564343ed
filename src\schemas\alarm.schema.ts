import mongoose, { Schema, models, type Model } from 'mongoose';
import type { AlarmInterface, ThresholdProperties } from 'interfaces';

const ThresholdPropertiesSchema = new Schema<ThresholdProperties>(
  {
    operator: { type: String },
    subsystemBrickClass: { type: String },
    pointBrickClass: { type: String },
    subsystemPartName: { type: String },
    subsystemName: { type: String },
    unitOfMeasure: { type: String },
    conditionValue: { type: Number },
    currentValue: { type: Number },
  },
  { _id: false },
);

const SiteAddressPartsSchema = new Schema({
  street: { type: String },
  city: { type: String },
  state: { type: String },
  zip: { type: String },
});

const AlarmSchema = new Schema<AlarmInterface>({
  siteId: { type: String, index: true },
  siteName: { type: String, index: true },
  siteAddress: { type: String, index: true },
  siteAddressParts: { type: SiteAddressPartsSchema },
  siteCountry: { type: String },
  siteMarket: { type: String },
  siteRegion: { type: String },
  siteCommercialTerritory: { type: String },
  siteOrganization: { type: String },
  assetAddress: { type: String },
  customerName: { type: String, allowNull: true },
  customerId: { type: String, allowNull: true },
  id: { type: String },
  assetId: { type: String, index: true },
  conditionId: { type: String },
  thresholdProperties: { type: ThresholdPropertiesSchema },
  timestamp: { type: Number },
  cioTags: { type: String },
  brickClass: { type: String },
  measureName: { type: String, index: true },
  measureValue: { type: Boolean },
  createdAt: { type: Number },
  resetAt: { type: Number },
  activatedAt: { type: Number },
  status: { type: String, index: true },
  acknowledgementId: {
    type: Schema.Types.ObjectId,
    ref: 'Acknowledgement',
    required: false,
  },
  groupId: { type: String, allowNull: true },
  modifiedBy: { type: String, allowNull: true },
  series: { type: String, allowNull: true },
  controller: { type: String, allowNull: true },
  code: { type: String, allowNull: true },
  criticality: { type: Schema.Types.Number, allowNull: true },
  possibleCause: { type: String, allowNull: true },
  description: { type: String, allowNull: true },
  type: { type: String, allowNull: true },
  category: { type: String, allowNull: true },
  resetType: { type: String, allowNull: true },
  chillerName: { type: String, allowNull: true, index: true },
  serialNumber: { type: String, allowNull: true, index: true },
  model: { type: String, allowNull: true, index: true },
  modelNumber: { type: String, allowNull: true, index: true },
  fullCode: { type: String, allowNull: true },
  totalChillers: { type: Schema.Types.Number, allowNull: true },
  runStatus: { type: String, allowNull: true },
  capacity: { type: String, allowNull: true },
  isMuted: { type: Boolean, allowNull: true },
  lookupKey: { type: String, allowNull: true },
  edgeId: { type: String, allowNull: true },
  isEdgeOnline: { type: Boolean, allowNull: true },
});

export const AlarmModel: Model<AlarmInterface> =
  models.Alarm || mongoose.model<AlarmInterface>('Alarm', AlarmSchema, 'Alarms');
