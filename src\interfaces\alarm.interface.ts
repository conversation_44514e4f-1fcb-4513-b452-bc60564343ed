import { Document, Types } from 'mongoose';
import { LANGUAGE_LOCALE } from '../enums/languageLocale.enum';
import { AcknowledgementInterface } from './acknowledgement.interface';

export interface ThresholdProperties {
  operator: string | null;
  subsystemBrickClass: string | null;
  subsystemPartName: string | null;
  subsystemName: string | null;
  unitOfMeasure: string | null;
  conditionValue: number | null;
  currentValue: number | null;
  pointBrickClass: string | null;
}

type Translations = {
  [key in LANGUAGE_LOCALE]?: {
    possibleCause?: string;
    text?: string;
  };
};

interface Location {
  street: string | null;
  city: string | null;
  state: string | null;
  zip: string | null;
}

export interface ExtendedAlarmMetaData {
  translations?: Translations;
  location?: Location;
}

export interface SiteAddressParts {
  street: string | null;
  city: string | null;
  state: string | null;
  zip: string | null;
}

export interface AlarmInterface extends Omit<Document<Types.ObjectId>, 'model'> {
  siteId: string | null;
  siteName: string | null;
  siteAddress: string | null;
  siteAddressParts: SiteAddressParts | null;
  siteCountry: string | null;
  siteMarket: string | null;
  siteRegion: string | null;
  siteCommercialTerritory: string | null;
  siteOrganization: string | null;
  customerName: string | null;
  customerId: string | null;
  id: string;
  assetId: string;
  conditionId: string | null;
  thresholdProperties: ThresholdProperties | null;
  timestamp: number;
  cioTags: string;
  brickClass: string;
  measureName: string;
  measureValue: boolean;
  createdAt: number;
  status: string;
  groupId: string | null;
  modifiedBy: string | null;
  criticality: number | null;
  series: string | null;
  controller: string | null;
  code: string | null;
  acknowledgementId: Types.ObjectId;
  possibleCause: string | null;
  description: string | null;
  type: string | null;
  category: string | null;
  resetType: string | null;
  activatedAt: number | null;
  resetAt: number | null;
  chillerName: string | null;
  serialNumber: string | null;
  model: string | null;
  modelNumber: string | null;
  fullCode: string | null;
  totalChillers: number;
  groupedCount: number | null;
  runStatus: string | null;
  capacity: string | null;
  isMuted: boolean | null;
  lookupKey: string | null;
  acknowledgement: AcknowledgementInterface | null;
  extendedAlarmMetaData?: ExtendedAlarmMetaData | null;
  assetAddress?: string | null;
  edgeId?: string | null;
  isEdgeOnline?: boolean | null;
}
