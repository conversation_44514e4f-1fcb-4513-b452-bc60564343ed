import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { AcknowledgementRepository } from '../../repositories/acknowledgement.repository';
import { AcknowledgementModel } from '../../schemas/acknowledgement.schema';
import { AcknowledgementMockData, NewAcknowledgementMockData } from '../mock/Acknowledgement.mock';

describe('AcknowledgementRepository', () => {
  let mongoServer: MongoMemoryServer;
  let repository: AcknowledgementRepository;

  beforeAll(async () => {
    const actualMongoose = jest.requireActual('mongoose');
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await actualMongoose.connect(uri);
  });

  beforeEach(async () => {
    await AcknowledgementModel.insertMany(AcknowledgementMockData);
    repository = new AcknowledgementRepository(AcknowledgementModel);
  });

  afterEach(async () => {
    await AcknowledgementModel.deleteMany({});
  });

  it('should return all acknowledgements', async () => {
    const acknowledgements = await repository.findAll();
    expect(acknowledgements.length).toBeGreaterThan(0);
  });

  it('should return acknowledgements filtered by siteId', async () => {
    const siteId = AcknowledgementMockData[0].siteId;
    const acknowledgements = await repository.findManyBySiteId(siteId);
    expect(acknowledgements[0].siteId).toEqual(siteId);
  });

  it('should return last acknowledgements by site list', async () => {
    const siteIds = [AcknowledgementMockData[0].siteId];
    const fromTimestamp = AcknowledgementMockData[0].acknowledgedAt - 1000;
    const acknowledgements = await repository.getLastAcknowledgementsBySiteList(siteIds, fromTimestamp);
    expect(acknowledgements.length).toBeGreaterThan(0);
  });

  it('should create an acknowledgement', async () => {
    const newAcknowledgement = await repository.create(NewAcknowledgementMockData);
    expect(newAcknowledgement._id).toBeDefined();
  });

  it('should update an acknowledgement', async () => {
    const ack = await repository.create(NewAcknowledgementMockData);
    const updatedAck = await repository.update(ack._id?.toString() ?? '', { caseId: 'updated-case' });
    expect(updatedAck?.caseId).toBe('updated-case');
  });

  it('should delete an acknowledgement', async () => {
    const ack = await repository.create(NewAcknowledgementMockData);
    await repository.delete(ack._id?.toString() ?? '');
    const deletedAck = await repository.findById(ack._id?.toString() ?? '');
    expect(deletedAck).toBeNull();
  });

  it('should return filtered acknowledgements with pagination', async () => {
    const filter = { caseId: "00719878" };
    const projection = { status: 1, caseId: 1, siteRegion: 1 };
    const page = { limit: 2, offset: 0 };
    const authScopeFilter = [{field: 'siteRegion', options: ['India']}]

    const results = await repository.findByFilter(filter, projection, page, authScopeFilter);

    expect(results.data.length).toBe(1);
    expect(results.data[0]).toHaveProperty('status');
    expect(results.data[0]).not.toHaveProperty('siteId');
  });

  // it('should return case summary', async () => {
  //   const authScopeFilter = [{field: 'siteRegion', options: ['India']}];
  //   const results = await repository.countCaseSummary(authScopeFilter);

  //   expect(results[0]).toHaveProperty('name');
  //   expect(results[0]).toHaveProperty('count');
  // });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });
});