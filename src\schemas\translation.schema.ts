import mongoose, { Schema, models, Model } from 'mongoose';
import { IOperator, ITranslation } from 'interfaces';

export const tableName = 'Translations';

const OperatorSchema = new Schema<IOperator>(
  {
    gt: { type: String, required: false },
    lt: { type: String, required: false },
  },
  { _id: false },
);

const TranslationSchema = new Schema<ITranslation>({
  language: { type: String, required: true },
  possibleCauseTemplate: { type: String, required: false },
  descriptionTextTemplate: { type: String, required: false },
  operator: { type: OperatorSchema, required: false },
});

export const Translation: Model<ITranslation> =
  models[tableName] || mongoose.model<ITranslation>(tableName, TranslationSchema, tableName);
