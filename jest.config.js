module.exports = {
  preset: 'ts-jest',
  testMatch: ['**/+(*.)+(spec|test).+(ts|js)?(x)'],
  resolver: 'ts-jest-resolver',
  coverageReporters: ['lcov'],
  coverageDirectory: './coverage',
  globals: {
    'ts-jest': {
      tsconfig: './tsconfig.json',
    },
  },
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]sx?$': 'ts-jest',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  testPathIgnorePatterns: [
    './src/__tests__/fixtures',
    './src/__tests__/_utils',
  ],
  setupFiles: ['./src/__tests__/jest.setupEnv.ts'],
  testTimeout: 100000
};
