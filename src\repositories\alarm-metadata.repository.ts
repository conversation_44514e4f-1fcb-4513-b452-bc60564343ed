import { ClientSession, Model, Types, Document } from 'mongoose';
import { BulkWriteResult } from 'mongodb';
import { AlarmMetadataInterface } from 'interfaces/alarm-metadata.interface';

export class AlarmMetadataRepository {
  // eslint-disable-next-line
  constructor(private readonly model: Model<AlarmMetadataInterface>) {}

  async findAll(): Promise<AlarmMetadataInterface[]> {
    return this.model.find().exec();
  }

  async findByAlarmIdAndCode(alarmId: string, alarmCode: string): Promise<AlarmMetadataInterface | null> {
    return this.model.findOne({ alarmId, alarmCode }).exec();
  }

  async findById(_id: string): Promise<AlarmMetadataInterface | null> {
    return this.model.findById(_id).exec();
  }

  async create(payload: any, session?: ClientSession): Promise<AlarmMetadataInterface | null> {
    const [response] = await this.model.create([payload], { session });

    return response;
  }

  async update(_id: string, dto: any, session?: ClientSession): Promise<AlarmMetadataInterface | null> {
    await this.model
      .updateOne(
        {
          _id: new Types.ObjectId(_id),
        },
        dto,
        { session, new: true },
      )
      .exec();

    return this.findById(_id);
  }

  async updateMany(
    _ids: string[],
    acknowledgementId: string,
    session?: ClientSession,
  ): Promise<AlarmMetadataInterface[]> {
    const objectIds = _ids.map((_id) => {
      return new Types.ObjectId(_id);
    });
    await this.model
      .updateMany(
        {
          _id: { $in: objectIds },
        },
        { acknowledgement: acknowledgementId },
        { session, new: true },
      )
      .exec();

    return this.model.find({ _id: { $in: _ids } }).exec();
  }

  async delete(_id: string, session?: ClientSession): Promise<AlarmMetadataInterface | null> {
    return this.model.findByIdAndDelete(_id, { session }).exec();
  }

  async createOrUpdate(_id: string, payload: any): Promise<AlarmMetadataInterface | null> {
    await this.model.updateOne({ _id: new Types.ObjectId(_id) }, payload, {
      upsert: true,
      new: true,
    });

    return this.findById(_id);
  }

  async createOrUpdateBulk(
    payload: Omit<AlarmMetadataInterface, keyof Document<Types.ObjectId>>[],
    session?: ClientSession,
  ): Promise<BulkWriteResult> {
    const bulkOps = payload.map((doc) => ({
      updateOne: {
        filter: {
          alarmId: doc.alarmId,
          alarmCode: doc.alarmCode,
        },
        update: doc,
        upsert: true,
      },
    }));

    return this.model.bulkWrite(bulkOps, { session });
  }
}
