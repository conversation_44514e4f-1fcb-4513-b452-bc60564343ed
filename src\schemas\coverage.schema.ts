import mongoose, { Schema, models, Model } from 'mongoose';
import { CoverageInterface } from 'interfaces';

export const tableName = 'Coverage';

const CoverageSchema = new Schema<CoverageInterface>({
  warrantyType: { type: String, required: false },
  warrantyExpDate: { type: Number, required: false },
  extendedWarrantyExpDate: { type: Number, required: false },
  contractId: { type: String, required: false },
  contractType: { type: String, required: false },
  contractEndDate: { type: Number, required: false },
  contractStatus: { type: String, required: false },
  contractCanceledDate: { type: Number, required: false },
  assetId: { type: String, required: true },
  serialNumber: { type: String, required: true },
  startDate: { type: Number, required: true },
  endDate: { type: Number, required: false },
});

CoverageSchema.index({ startDate: -1 });

export const Coverage: Model<CoverageInterface> =
  models[tableName] || mongoose.model<CoverageInterface>(tableName, CoverageSchema, tableName);
